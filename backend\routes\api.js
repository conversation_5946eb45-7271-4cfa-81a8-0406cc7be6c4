const express = require('express');
const router = express.Router();

// Import controllers
const { getYears } = require('../controllers/yearsController');
const { getMakesByYear } = require('../controllers/makesController');
const { getModelsByYearAndMake } = require('../controllers/modelsController');
const { getTypesByYearMakeModel } = require('../controllers/typesController');
const { getCompatibility } = require('../controllers/compatibilityController');
const { upload, uploadImage, getImage, updateLedguysData } = require('../controllers/imageController');
const { upload: csvUpload, uploadCSV, importCSV, getCSVFormat } = require('../controllers/uploadController');

// Define routes
router.get('/years', getYears);
router.get('/makes', getMakesByYear);
router.get('/models', getModelsByYearAndMake);
router.get('/types', getTypesByYearMakeModel);
router.get('/compatibility', getCompatibility);

// Image routes
router.post('/images/:compatibilityId', upload.single('image'), uploadImage);
router.get('/images/:filename', getImage);

// LED Guys data routes
router.put('/compatibility/:id/ledguys-data', updateLedguysData);

// CSV upload routes
router.post('/upload/import', importCSV);
router.post('/upload/:fileType', csvUpload.single('file'), uploadCSV);
router.get('/upload/format/:fileType', getCSVFormat);

// Health check route
router.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'API is running' });
});

module.exports = router;
