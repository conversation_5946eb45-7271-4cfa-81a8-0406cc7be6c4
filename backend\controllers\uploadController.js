const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { importCSVFile } = require('../utils/csvImporter');

// Configure multer for CSV file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../data');

    // Create the data directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Use the file type as the filename (makes.csv, models.csv, etc.)
    const fileType = req.params.fileType;
    cb(null, `${fileType}.csv`);
  }
});

// File filter to only allow CSV files
const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
    cb(null, true);
  } else {
    cb(new Error('Only CSV files are allowed!'), false);
  }
};

// Create the multer upload instance with limits for large files
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 * 1024, // 10GB limit
    fieldSize: 10 * 1024 * 1024 * 1024, // 10GB field size limit
  }
});

/**
 * Upload a CSV file
 * @route POST /api/upload/:fileType
 * @param {string} fileType - The type of file (makes, models, types, compatibility)
 * @access Public
 */
const uploadCSV = async (req, res) => {
  const { fileType } = req.params;

  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  try {
    console.log(`File ${fileType}.csv uploaded successfully`);

    return res.status(201).json({
      message: `${fileType}.csv uploaded successfully`,
      filename: req.file.filename,
      size: req.file.size,
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({
      message: 'Server error while uploading file',
      error: error.message
    });
  }
};

/**
 * Import uploaded CSV files into the database
 * @route POST /api/upload/import
 * @access Public
 */
const importCSV = async (req, res) => {
  // Set up SSE for progress updates if supported
  let progressCallback = null;

  try {
    // Get fileType from request body, default to compatibility
    let { fileType } = req.body;

    // Only supporting compatibility file type as per requirements
    if (!fileType || fileType !== 'compatibility') {
      fileType = 'compatibility';
    }

    // Import the compatibility file
    console.log(`Importing ${fileType}.csv...`);

    // Check if the file exists
    const filePath = path.join(__dirname, '../data', `${fileType}.csv`);
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        message: `File ${fileType}.csv not found. Please upload the file first.`
      });
    }

    // Get file size for logging
    const stats = fs.statSync(filePath);
    const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`File size: ${fileSizeMB} MB`);

    // If file is very large, send a warning in the response
    if (stats.size > 1024 * 1024 * 1024) { // 1GB
      console.log('Large file detected. Import may take several minutes.');
    }

    // Create progress callback
    progressCallback = (progress) => {
      console.log(`Import progress: ${progress}%`);
    };

    // Import the file with progress tracking
    const result = await importCSVFile(fileType, progressCallback);

    console.log(`Imported ${result.inserted} ${fileType} records`);

    // Add file size info to the details
    const details = `File size: ${fileSizeMB} MB\n${result.details || ''}`;

    return res.status(200).json({
      message: `${fileType}.csv imported successfully`,
      processed: result.processed,
      inserted: result.inserted,
      details: details
    });
  } catch (error) {
    console.error('Error importing CSV data:', error);

    // Provide more detailed error information
    let errorMessage = error.message;
    let errorDetails = '';

    // Check for specific error types
    if (error.code === 'ENOENT') {
      errorMessage = 'File not found. Please upload the file first.';
    } else if (error.code === 'EACCES') {
      errorMessage = 'Permission denied when accessing the file.';
    } else if (error.code === 'EMFILE') {
      errorMessage = 'Too many open files. The system cannot open more files.';
    } else if (error.code === 'EPERM') {
      errorMessage = 'Operation not permitted.';
    } else if (error.code === '23505') {
      errorMessage = 'Duplicate key violation. Some records already exist in the database.';
    } else if (error.code === '22P02') {
      errorMessage = 'Invalid data format in CSV file.';
    } else if (error.code === '42P01') {
      errorMessage = 'Table does not exist. Database schema may be outdated.';
    }

    // Add stack trace in development mode
    if (process.env.NODE_ENV === 'development') {
      errorDetails = error.stack;
    }

    return res.status(500).json({
      message: 'Server error while importing CSV data',
      error: errorMessage,
      details: errorDetails
    });
  }
};

/**
 * Get CSV file format information
 * @route GET /api/upload/format/:fileType
 * @param {string} fileType - The type of file (makes, models, types, compatibility)
 * @access Public
 */
const getCSVFormat = async (req, res) => {
  const { fileType } = req.params;

  try {
    // Only supporting compatibility CSV format as per requirements
    const formats = {
      compatibility: {
        requiredColumns: ['Year', 'Make', 'Model', 'SubModel', 'Qualifier', 'FullName', 'Id', 'Name', 'Type', 'Displaycode', 'PackageId', 'PackageName', 'PackageCode', 'PackageShowPart'],
        sampleData: [
          {
            Year: '2023',
            Make: 'Acura',
            Model: 'RDX',
            SubModel: 'Premium',
            Qualifier: 'AWD',
            FullName: 'Acura RDX Premium AWD',
            Id: 'B123',
            Name: 'Headlight',
            Type: 'LED',
            Displaycode: 'A-RDX-HL-23',
            PackageId: 'PKG001',
            PackageName: 'Premium LED Headlight',
            PackageCode: 'HL-LED-001',
            PackageShowPart: 'HL-LED-001-SHOW'
          }
        ],
        description: 'CSV file containing compatibility information. All columns are required.'
      }
    };

    // Check if the requested file type exists
    if (!formats[fileType]) {
      return res.status(404).json({ message: 'File type not found' });
    }

    return res.json(formats[fileType]);

  } catch (error) {
    console.error('Error getting CSV format:', error);
    return res.status(500).json({
      message: 'Server error while getting CSV format',
      error: error.message
    });
  }
};

module.exports = {
  upload,
  uploadCSV,
  importCSV,
  getCSVFormat
};
