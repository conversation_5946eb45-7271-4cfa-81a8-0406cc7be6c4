{"name": "ledguys-bulb-lookup", "version": "1.0.0", "description": "LED Guys Vehicle LED Bulb Lookup Tool", "engines": {"node": ">=20.0.0"}, "scripts": {"install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "backend": "cd backend && npm run dev", "frontend": "cd frontend && npm start", "frontend-legacy": "cd frontend && npm run start-legacy", "dev": "concurrently \"npm run backend\" \"npm run frontend\"", "dev-legacy": "concurrently \"npm run backend\" \"npm run frontend-legacy\"", "import-data": "cd backend && npm run import-data"}, "dependencies": {"concurrently": "^8.2.2"}}