# Detailed User Flow

## 1. Initial State
- App loads with empty selection fields
- Only Year dropdown and Connect button active
- "Nothing to display" message shown

## 2. Connection Flow
- User clicks Connect button
- Database connection established
- Years (2019-2024) populated in dropdown

## 3. Selection Process
a) Year Selection:
   - User selects year
   - Makes dropdown populated
   - Shows "Loading Makes..." message

b) Make Selection:
   - User selects make (e.g., Acura)
   - Models dropdown populated
   - Shows "Loading Models..." message

c) Model Selection:
   - User selects model (e.g., RDX)
   - Types dropdown populated (if applicable)
   - Shows "Loading Types..." message

d) Type Selection:
   - User selects type (e.g., Stepwell)
   - Final bulb information displayed in grid

## 4. Reset Scenarios
- Changing any selection resets subsequent fields
- Database timeout triggers reconnect prompt
- Error states show appropriate messages