/**
 * <PERSON><PERSON><PERSON> to update the schema to match the required field names
 * and add the missing dis_type field
 */
const { pool } = require('./connection');

const updateSchema = async () => {
  const client = await pool.connect();

  try {
    console.log('Starting schema update for field names...');

    // Begin transaction
    await client.query('BEGIN');

    // Check if dis_type column already exists in bulb_compatibility
    const checkDisTypeColumn = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'bulb_compatibility' AND column_name = 'dis_type'
    `);

    if (checkDisTypeColumn.rows.length === 0) {
      console.log('Adding dis_type column to bulb_compatibility table...');
      await client.query(`
        ALTER TABLE bulb_compatibility
        ADD COLUMN dis_type VARCHAR(50)
      `);
    } else {
      console.log('dis_type column already exists in bulb_compatibility table.');
    }

    // Create a new table with the correct field names
    console.log('Creating new table with correct field names...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS compatibility_new (
        id SERIAL PRIMARY KEY,
        car_year INTEGER CHECK (car_year BETWEEN 2019 AND 2025),
        car_make VARCHAR(50),
        car_model VARCHAR(50),
        blub_type VARCHAR(50),
        dis_type VARCHAR(50),
        dis_code VARCHAR(50),
        pack_id VARCHAR(50),
        pack_name VARCHAR(250),
        productvalue VARCHAR(50),
        ledguys_data VARCHAR(500),
        ledguys_desc VARCHAR(500),
        image_url VARCHAR(500)
      )
    `);

    // Check if the old table exists and has data
    const checkOldTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'bulb_compatibility'
      )
    `);

    if (checkOldTable.rows[0].exists) {
      // Check if the old table has the old schema (with make_id, model_id, etc.)
      const checkOldSchema = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_name = 'bulb_compatibility' AND column_name = 'make_id'
        )
      `);

      if (checkOldSchema.rows[0].exists) {
        // Old schema exists, need to migrate data
        console.log('Old schema detected, migrating data...');

        // Check if the required tables exist
        const checkMakesTable = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'makes'
          )
        `);

        const checkModelsTable = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'models'
          )
        `);

        const checkTypesTable = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'types'
          )
        `);

        if (checkMakesTable.rows[0].exists && checkModelsTable.rows[0].exists && checkTypesTable.rows[0].exists) {
          // Copy data from old table to new table
          console.log('Copying data from old table to new table...');
          await client.query(`
            INSERT INTO compatibility_new (
              car_year, car_make, car_model, blub_type, dis_type, dis_code,
              pack_id, pack_name, productvalue, ledguys_data, ledguys_desc, image_url
            )
            SELECT
              year,
              (SELECT name FROM makes WHERE id = make_id),
              (SELECT name FROM models WHERE id = model_id),
              (SELECT name FROM types WHERE id = type_id),
              '', -- dis_type (empty for now)
              display_code,
              package_id,
              package_name,
              package_code,
              ledguys_data,
              ledguys_desc,
              image_url
            FROM bulb_compatibility
          `);

          // Get count of copied records
          const countResult = await client.query('SELECT COUNT(*) FROM compatibility_new');
          console.log(`Copied ${countResult.rows[0].count} records to new table.`);
        } else {
          console.log('Required reference tables not found, skipping data migration.');
        }
      } else {
        // New schema already exists, just need to add dis_type if it doesn't exist
        console.log('New schema already detected, checking for dis_type column...');

        const checkDisTypeColumn = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'bulb_compatibility' AND column_name = 'dis_type'
        `);

        if (checkDisTypeColumn.rows.length === 0) {
          console.log('Adding dis_type column to existing bulb_compatibility table...');
          await client.query(`
            ALTER TABLE bulb_compatibility
            ADD COLUMN dis_type VARCHAR(50)
          `);
        } else {
          console.log('dis_type column already exists in bulb_compatibility table.');
        }

        // No need to create a new table, just exit
        console.log('Schema is already up to date.');
        await client.query('COMMIT');
        return;
      }

      // Rename tables
      console.log('Renaming tables...');
      await client.query('ALTER TABLE bulb_compatibility RENAME TO bulb_compatibility_old');
      await client.query('ALTER TABLE compatibility_new RENAME TO bulb_compatibility');

      // Update sequences
      console.log('Updating sequences...');
      await client.query(`
        SELECT setval('bulb_compatibility_id_seq', (SELECT MAX(id) FROM bulb_compatibility), true)
      `);
    } else {
      console.log('Old table does not exist, creating new table...');
      // Just rename the new table
      await client.query('ALTER TABLE compatibility_new RENAME TO bulb_compatibility');
    }

    // Commit transaction
    await client.query('COMMIT');
    console.log('Schema update completed successfully!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating schema:', error);
  } finally {
    client.release();
  }
};

// Run the update
updateSchema();
