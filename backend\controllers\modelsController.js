const db = require('../db/connection');

/**
 * Get models by year and make
 * @route GET /api/models
 * @query year - The year to filter by
 * @query make - The make ID to filter by
 * @access Public
 */
const getModelsByYearAndMake = async (req, res) => {
  const { year, make } = req.query;

  if (!year || !make) {
    res.status(400);
    throw new Error('Year and make parameters are required');
  }

  try {
    // Query to get all models for the specified year and make
    const query = `
      SELECT DISTINCT car_model as name
      FROM bulb_compatibility
      WHERE car_year = $1 AND car_make = $2
      ORDER BY car_model ASC
    `;

    const { rows } = await db.query(query, [year, make]);

    res.json(rows);
  } catch (error) {
    console.error('Error fetching models:', error);
    res.status(500);
    throw new Error('Server error while fetching models');
  }
};

module.exports = {
  getModelsByYearAndMake,
};
