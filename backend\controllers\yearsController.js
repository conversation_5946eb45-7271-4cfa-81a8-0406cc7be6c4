const db = require('../db/connection');

/**
 * Get all available years dynamically from the database
 * @route GET /api/years
 * @access Public
 */
const getYears = async (req, res) => {
  try {
    // Query the database for all available years
    const query = `
      SELECT DISTINCT car_year as year
      FROM bulb_compatibility
      WHERE car_year IS NOT NULL
      ORDER BY car_year DESC
    `;

    const { rows } = await db.query(query);

    // Extract years from the result rows
    const years = rows.map(row => row.year);

    // If no years found in the database, return an empty array
    // This will indicate to the frontend that no data is available yet
    if (years.length === 0) {
      console.log('No years found in database, returning empty array');
      return res.json([]);
    }

    res.json(years);
  } catch (error) {
    console.error('Error fetching years:', error);
    res.status(500).json({ message: 'Server error while fetching years' });
  }
};

module.exports = {
  getYears,
};
