/**
 * <PERSON><PERSON><PERSON> to update the year constraint in the bulb_compatibility table
 * to allow a wider range of years (2000-2025)
 */
const { pool } = require('./connection');

const updateYearConstraint = async () => {
  const client = await pool.connect();

  try {
    console.log('Starting update of year constraint...');

    // Begin transaction
    await client.query('BEGIN');

    // Check if the constraint exists
    const checkConstraint = await client.query(`
      SELECT conname
      FROM pg_constraint
      WHERE conrelid = 'bulb_compatibility'::regclass
      AND conname LIKE '%year%check'
    `);

    if (checkConstraint.rows.length > 0) {
      const constraintName = checkConstraint.rows[0].conname;
      console.log(`Found constraint: ${constraintName}`);

      // Drop the existing constraint
      console.log('Dropping existing year constraint...');
      await client.query(`
        ALTER TABLE bulb_compatibility
        DROP CONSTRAINT ${constraintName}
      `);

      // Add the new constraint with a wider range
      console.log('Adding new year constraint with wider range (2000-2025)...');
      await client.query(`
        ALTER TABLE bulb_compatibility
        ADD CONSTRAINT bulb_compatibility_car_year_check
        CHECK (car_year BETWEEN 2000 AND 2025)
      `);

      console.log('Year constraint updated successfully!');
    } else {
      console.log('No year constraint found, adding new constraint...');
      
      // Add the new constraint with a wider range
      await client.query(`
        ALTER TABLE bulb_compatibility
        ADD CONSTRAINT bulb_compatibility_car_year_check
        CHECK (car_year BETWEEN 2000 AND 2025)
      `);
      
      console.log('Year constraint added successfully!');
    }

    // Commit transaction
    await client.query('COMMIT');
    console.log('Update completed successfully!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating year constraint:', error);
  } finally {
    client.release();
    // Close the pool
    pool.end();
  }
};

// Run the update
updateYearConstraint();
