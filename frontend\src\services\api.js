import axios from 'axios';

// Create axios instance with base URL and extended timeout
const api = axios.create({
  // In production, this will use the relative path '/api' which avoids CORS issues
  // In development, it will use the full URL from the environment variable or localhost
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : (process.env.REACT_APP_API_URL || 'http://localhost:5000/api'),
  timeout: 3600000, // 1 hour timeout for large operations
  // Increase max content length and max body length for large requests
  maxContentLength: Infinity,
  maxBodyLength: Infinity
});

// Connection timeout (1 hour in milliseconds)
const CONNECTION_TIMEOUT = 60 * 60 * 1000; // 1 hour

let connectionTimer = null;

// API service functions
const apiService = {
  // Connect to the database
  connect: async () => {
    try {
      const response = await api.get('/connect');

      // Start the connection timeout timer
      if (connectionTimer) clearTimeout(connectionTimer);
      connectionTimer = setTimeout(() => {
        // Dispatch a custom event when connection times out
        window.dispatchEvent(new CustomEvent('db-connection-timeout'));
      }, CONNECTION_TIMEOUT);

      return response.data;
    } catch (error) {
      console.error('Connection error:', error);
      throw error;
    }
  },

  // Disconnect from the database (clear the timeout)
  disconnect: () => {
    if (connectionTimer) {
      clearTimeout(connectionTimer);
      connectionTimer = null;
    }
  },

  // Get all available years
  getYears: async () => {
    try {
      const response = await api.get('/years');
      return response.data;
    } catch (error) {
      console.error('Error fetching years:', error);
      throw error;
    }
  },

  // Get makes by year
  getMakesByYear: async (year) => {
    try {
      const response = await api.get(`/makes?year=${year}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching makes:', error);
      throw error;
    }
  },

  // Get models by year and make
  getModelsByYearAndMake: async (year, make) => {
    try {
      const response = await api.get(`/models?year=${year}&make=${make}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching models:', error);
      throw error;
    }
  },

  // Get types by year, make, and model
  getTypesByYearMakeModel: async (year, make, model) => {
    try {
      const response = await api.get(`/types?year=${year}&make=${make}&model=${model}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching types:', error);
      throw error;
    }
  },

  // Get compatibility information
  getCompatibility: async (year, make, model, type, page = 1, limit = 20) => {
    try {
      let url = `/compatibility?year=${year}&make=${make}&model=${model}&page=${page}&limit=${limit}`;

      // Only add the type parameter if it's provided
      if (type) {
        url += `&type=${type}`;
        console.log(`Fetching compatibility for year=${year}, make=${make}, model=${model}, type=${type}, page=${page}`);
      } else {
        console.log(`Fetching compatibility for year=${year}, make=${make}, model=${model} without type, page=${page}`);
      }

      const response = await api.get(url);

      console.log('Compatibility data received:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching compatibility:', error);
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error setting up request:', error.message);
      }
      throw error;
    }
  },

  // Upload an image for a compatibility record
  uploadImage: async (compatibilityId, formData) => {
    try {
      const response = await api.post(`/images/${compatibilityId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  },

  // Update LED Guys data for a compatibility record
  updateLedguysData: async (compatibilityId, ledguysData, ledguysDesc) => {
    try {
      const response = await api.put(`/compatibility/${compatibilityId}/ledguys-data`, {
        ledguysData,
        ledguysDesc,
      });
      return response.data;
    } catch (error) {
      console.error('Error updating LED Guys data:', error);
      throw error;
    }
  },

  // Get CSV file format information
  getCSVFormat: async (fileType) => {
    try {
      const response = await api.get(`/upload/format/${fileType}`);
      return response.data;
    } catch (error) {
      console.error('Error getting CSV format:', error);
      throw error;
    }
  },

  // Upload a CSV file
  uploadCSV: async (fileType, file, onProgress) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post(`/upload/${fileType}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(percentCompleted);
          }
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading CSV file:', error);
      throw error;
    }
  },

  // Import uploaded CSV files into the database
  importCSV: async (fileType = null) => {
    try {
      // If fileType is provided, send it in the request body
      const response = await api.post('/upload/import', fileType ? { fileType } : {}, {
        // Set a longer timeout for large imports
        timeout: 3600000 // 1 hour
      });
      return response.data;
    } catch (error) {
      console.error('Error importing CSV data:', error);
      throw error;
    }
  },
};

export default apiService;
