/**
 * <PERSON><PERSON><PERSON> to update the schema to allow 2025 as a valid year
 */
const { pool } = require('./connection');

const updateSchema = async () => {
  const client = await pool.connect();
  
  try {
    console.log('Updating schema to allow 2025 as a valid year...');
    
    // Start a transaction
    await client.query('BEGIN');
    
    // Drop the existing constraint
    await client.query(`
      ALTER TABLE bulb_compatibility 
      DROP CONSTRAINT IF EXISTS bulb_compatibility_year_check
    `);
    
    // Add the new constraint
    await client.query(`
      ALTER TABLE bulb_compatibility 
      ADD CONSTRAINT bulb_compatibility_year_check 
      CHECK (year BETWEEN 2019 AND 2025)
    `);
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Schema updated successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating schema:', error);
  } finally {
    client.release();
  }
};

// Run the function and exit
updateSchema()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
