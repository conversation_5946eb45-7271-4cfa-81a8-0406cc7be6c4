/**
 * CSV Importer Utility
 *
 * This utility provides functions for importing large CSV files into PostgreSQL
 * using streaming and the COPY command for optimal performance.
 */

const fs = require('fs');
const path = require('path');
const { pipeline } = require('stream/promises');
const { parse } = require('csv-parse');
const { from } = require('pg-copy-streams');
const { Transform } = require('stream');
const { pool } = require('../db/connection');

/**
 * Import a CSV file into the database using PostgreSQL's COPY command
 *
 * @param {string} fileType - The type of file (makes, models, types, compatibility)
 * @param {Function} progressCallback - Optional callback for progress updates
 * @returns {Promise<Object>} - Import results
 */
const importCSVFile = async (fileType, progressCallback = null) => {
  // Get the file path
  const filePath = path.join(__dirname, '../data', `${fileType}.csv`);

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    throw new Error(`CSV file not found: ${fileType}.csv. Please upload the file first.`);
  }

  // Get file size for progress tracking
  const stats = fs.statSync(filePath);
  const totalSize = stats.size;
  let processedSize = 0;

  // Create a client with a long timeout
  const client = await pool.connect();

  try {
    console.log(`Starting import of ${fileType}.csv...`);

    // Start a transaction
    await client.query('BEGIN');

    // Define table structure based on file type
    const tableStructure = getTableStructure(fileType);

    console.log(`Table structure for ${fileType}:`, tableStructure.fields);

    // Create a temporary table
    await client.query(`
      CREATE TEMP TABLE temp_import_${fileType} (
        ${tableStructure.columns.join(',\n        ')}
      )
    `);

    // Create a read stream for the CSV file with larger buffer for better performance with big files
    const fileStream = fs.createReadStream(filePath, {
      highWaterMark: 1024 * 1024, // 1MB chunks for better performance with large files
    });

    // Create a parser for the CSV with detailed error handling
    const parser = parse({
      columns: true,
      skip_empty_lines: true,
      trim: true,
      skip_records_with_error: true,
      bom: true, // Handle byte order mark for files from different systems
      on_record: (record, { lines }) => {
        // Log every 10000 records for progress tracking (reduced frequency for large files)
        if (lines % 10000 === 0) {
          console.log(`Processed ${lines} lines...`);
          // Also update progress callback if provided
          if (progressCallback && totalSize > 0) {
            // Estimate progress based on line count and average line size
            const estimatedProgress = Math.min(Math.round((lines * 100) / (totalSize / 200)), 95);
            progressCallback(estimatedProgress);
          }
        }
        return record;
      }
    });

    // Add error handler to the parser
    parser.on('error', (err) => {
      console.error('CSV parsing error:', err.message);
      // Continue processing despite errors
    });

    // Create a transform stream to format data and track progress
    const transformer = new Transform({
      objectMode: true,
      transform(record, encoding, callback) {
        try {
          let line;

          // For compatibility data, map CSV field names to database column names
          if (fileType === 'compatibility' && tableStructure.fieldMapping) {
            const mappedRecord = {};

            // Map each field from the CSV to the corresponding database column
            Object.keys(tableStructure.fieldMapping).forEach(csvField => {
              const dbColumn = tableStructure.fieldMapping[csvField];
              mappedRecord[dbColumn] = record[csvField] || '';
            });

            // Format the mapped record for insertion
            const values = Object.keys(tableStructure.fieldMapping).map(csvField => {
              const dbColumn = tableStructure.fieldMapping[csvField];
              const value = record[csvField] || '';
              // Escape values for CSV
              return `"${String(value).replace(/"/g, '""')}"`;
            });

            line = values.join(',') + '\n';
          } else {
            // For other data types, use the original approach
            const values = tableStructure.fields.map(field => {
              const value = record[field] || '';
              // Escape values for CSV
              return `"${String(value).replace(/"/g, '""')}"`;
            });

            line = values.join(',') + '\n';
          }

          // Update processed size for progress tracking
          processedSize += Buffer.byteLength(JSON.stringify(record));

          // Call progress callback if provided
          if (progressCallback && totalSize > 0) {
            const progress = Math.min(Math.round((processedSize / totalSize) * 100), 99);
            progressCallback(progress);
          }

          callback(null, line);
        } catch (error) {
          console.error('Error transforming record:', error, record);
          // Continue with next record
          callback(null, '');
        }
      }
    });

    // Create a COPY stream with explicit format options
    const copyStream = client.query(from(`COPY temp_import_${fileType} FROM STDIN WITH (FORMAT CSV, DELIMITER ',', QUOTE '"', ESCAPE '"')`));

    // Set up the pipeline
    await pipeline(
      fileStream,
      parser,
      transformer,
      copyStream
    );

    // Count the imported rows
    const countResult = await client.query(`SELECT COUNT(*) FROM temp_import_${fileType}`);
    const importedCount = parseInt(countResult.rows[0].count);

    // Insert from temp table to the actual table
    const insertResult = await insertFromTempTable(client, fileType);

    // Commit the transaction
    await client.query('COMMIT');

    // Set progress to 100%
    if (progressCallback) {
      progressCallback(100);
    }

    return {
      processed: importedCount,
      inserted: insertResult.inserted,
      errors: insertResult.errors,
      details: insertResult.details
    };

  } catch (error) {
    // Rollback on error
    await client.query('ROLLBACK');
    throw error;
  } finally {
    // Drop the temp table if it exists
    try {
      await client.query(`DROP TABLE IF EXISTS temp_import_${fileType}`);
    } catch (err) {
      console.error(`Error dropping temp table temp_import_${fileType}:`, err);
    }

    // Release the client back to the pool
    client.release();
  }
};

/**
 * Get table structure based on file type
 *
 * @param {string} fileType - The type of file
 * @returns {Object} - Table structure with columns and fields
 */
const getTableStructure = (fileType) => {
  switch (fileType) {
    case 'makes':
      return {
        columns: ['name VARCHAR(100) NOT NULL'],
        fields: ['name'],
        table: 'makes'
      };
    case 'models':
      return {
        columns: [
          'make_name VARCHAR(100) NOT NULL',
          'name VARCHAR(100) NOT NULL'
        ],
        fields: ['make_name', 'name'],
        table: 'models'
      };
    case 'types':
      return {
        columns: ['name VARCHAR(100) NOT NULL'],
        fields: ['name'],
        table: 'types'
      };
    case 'compatibility':
      return {
        columns: [
          'car_year INTEGER',           // Year
          'car_make VARCHAR(50)',       // Make
          'car_model VARCHAR(50)',      // Model
          'sub_model VARCHAR(100)',     // SubModel
          'qualifier VARCHAR(100)',     // Qualifier
          'full_name VARCHAR(255)',     // FullName
          'bulb_id VARCHAR(50)',        // Id
          'bulb_name VARCHAR(100)',     // Name
          'blub_type VARCHAR(50)',      // Type
          'display_code VARCHAR(50)',   // Displaycode
          'pack_id VARCHAR(50)',        // PackageId
          'pack_name VARCHAR(250)',     // PackageName
          'pack_code VARCHAR(50)',      // PackageCode
          'pack_show_part VARCHAR(100)' // PackageShowPart
        ],
        fields: [
          'Year', 'Make', 'Model', 'SubModel', 'Qualifier', 'FullName',
          'Id', 'Name', 'Type', 'Displaycode', 'PackageId', 'PackageName',
          'PackageCode', 'PackageShowPart'
        ],
        fieldMapping: {
          'Year': 'car_year',
          'Make': 'car_make',
          'Model': 'car_model',
          'SubModel': 'sub_model',
          'Qualifier': 'qualifier',
          'FullName': 'full_name',
          'Id': 'bulb_id',
          'Name': 'bulb_name',
          'Type': 'blub_type',
          'Displaycode': 'display_code',
          'PackageId': 'pack_id',
          'PackageName': 'pack_name',
          'PackageCode': 'pack_code',
          'PackageShowPart': 'pack_show_part'
        },
        table: 'bulb_compatibility'
      };
    default:
      throw new Error(`Unknown file type: ${fileType}`);
  }
};

/**
 * Insert data from temporary table to the actual table
 *
 * @param {Object} client - Database client
 * @param {string} fileType - The type of file
 * @returns {Object} - Insert results
 */
const insertFromTempTable = async (client, fileType) => {
  const structure = getTableStructure(fileType);
  let insertQuery;
  let errors = [];
  let details = '';

  try {
    switch (fileType) {
      case 'makes':
        insertQuery = `
          INSERT INTO makes (name)
          SELECT name FROM temp_import_makes
          ON CONFLICT (name) DO NOTHING
          RETURNING id
        `;
        break;

      case 'models':
        // For models, we need to look up the make_id
        details += 'Processing models with make references...\n';
        insertQuery = `
          INSERT INTO models (make_id, name)
          SELECT m.id, t.name
          FROM temp_import_models t
          JOIN makes m ON t.make_name = m.name
          ON CONFLICT (make_id, name) DO NOTHING
          RETURNING id
        `;
        break;

      case 'types':
        insertQuery = `
          INSERT INTO types (name)
          SELECT name FROM temp_import_types
          ON CONFLICT (name) DO NOTHING
          RETURNING id
        `;
        break;

      case 'compatibility':
        details += 'Processing compatibility data...\n';
        // Direct insert into bulb_compatibility
        insertQuery = `
          INSERT INTO bulb_compatibility (
            car_year, car_make, car_model, sub_model, qualifier,
            full_name, bulb_id, bulb_name, blub_type, display_code,
            pack_id, pack_name, pack_code, pack_show_part
          )
          SELECT
            car_year, car_make, car_model, sub_model, qualifier,
            full_name, bulb_id, bulb_name, blub_type, display_code,
            pack_id, pack_name, pack_code, pack_show_part
          FROM temp_import_compatibility
          ON CONFLICT DO NOTHING
          RETURNING id
        `;
        break;

      default:
        throw new Error(`Unknown file type: ${fileType}`);
    }

    const insertResult = await client.query(insertQuery);
    const insertedCount = insertResult.rowCount;

    details += `Successfully inserted ${insertedCount} records into ${structure.table} table.\n`;

    return {
      inserted: insertedCount,
      errors: errors,
      details: details
    };
  } catch (error) {
    errors.push(error.message);
    details += `Error during insert: ${error.message}\n`;

    return {
      inserted: 0,
      errors: errors,
      details: details
    };
  }
};

module.exports = {
  importCSVFile
};
