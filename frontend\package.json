{"name": "ledguys-frontend", "version": "0.1.0", "private": true, "engines": {"node": ">=20.0.0"}, "dependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "axios": "^1.6.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "web-vitals": "^3.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start-legacy": "NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "build-legacy": "NODE_OPTIONS=--openssl-legacy-provider react-scripts build"}, "overrides": {"react-scripts": {"webpack": "5.88.2", "webpack-dev-server": "4.15.1"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}