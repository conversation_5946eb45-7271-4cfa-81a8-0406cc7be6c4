import React from 'react';
import Select from 'react-select';
import '../styles/ReactSelect.css';

const ReactSelectDropdown = ({
  options,
  value,
  onChange,
  placeholder,
  isDisabled = false,
  isLoading = false,
  isClearable = true
}) => {
  // Convert the value to the format expected by react-select
  const selectedValue = value ?
    options.find(option => option.value === value) :
    null;

  // Custom styles to match the application design
  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: '38px',
      border: state.isFocused
        ? '1px solid #0056b3'
        : '1px solid #ddd',
      borderRadius: '6px',
      boxShadow: state.isFocused
        ? '0 0 0 2px rgba(0, 86, 179, 0.2)'
        : 'none',
      '&:hover': {
        border: '1px solid #0056b3',
        cursor: 'pointer'
      },
      backgroundColor: isDisabled ? '#f5f5f5' : '#fff',
      fontSize: '0.9rem',
      transition: 'all 0.2s ease'
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#0056b3'
        : state.isFocused
          ? 'rgba(0, 86, 179, 0.08)'
          : null,
      color: state.isSelected ? 'white' : '#333',
      cursor: 'pointer',
      fontSize: '0.9rem',
      padding: '10px 12px',
      '&:hover': {
        backgroundColor: state.isSelected ? '#0056b3' : 'rgba(0, 86, 179, 0.08)'
      },
      '&:active': {
        backgroundColor: state.isSelected ? '#003d80' : 'rgba(0, 86, 179, 0.15)'
      }
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
      borderRadius: '6px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      overflow: 'hidden',
      border: '1px solid #eee'
    }),
    menuList: (provided) => ({
      ...provided,
      padding: '5px'
    }),
    indicatorSeparator: (provided) => ({
      ...provided,
      backgroundColor: '#e0e0e0'
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      color: '#6c757d',
      padding: '6px',
      '&:hover': {
        color: '#0056b3'
      }
    }),
    clearIndicator: (provided) => ({
      ...provided,
      color: '#6c757d',
      padding: '6px',
      '&:hover': {
        color: '#dc3545'
      }
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#999',
      fontSize: '0.9rem'
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#333',
      fontSize: '0.9rem',
      fontWeight: '500'
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: '2px 8px'
    })
  };

  return (
    <Select
      value={selectedValue}
      onChange={(selected) => onChange(selected ? selected.value : null)}
      options={options}
      placeholder={placeholder}
      isDisabled={isDisabled}
      isLoading={isLoading}
      isClearable={isClearable}
      className="react-select-container"
      classNamePrefix="react-select"
      styles={customStyles}
      menuPortalTarget={document.body}
      menuPosition="fixed"
    />
  );
};

export default ReactSelectDropdown;
