const { Pool } = require('pg');
require('dotenv').config();

// Create a connection pool with extended timeouts for large operations
const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  // Set idle timeout to 1 hour (3,600,000 ms) for large operations
  idleTimeoutMillis: parseInt(process.env.CONNECTION_TIMEOUT) || 3600000,
  // Set statement timeout to 1 hour
  statement_timeout: 3600000,
  // Set query timeout to 1 hour
  query_timeout: 3600000,
  // Increase max client connections
  max: 20,
});

// Connection event handlers
pool.on('connect', () => {
  console.log('Connected to the database');
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Function to check if connection is alive
const checkConnection = async () => {
  try {
    const client = await pool.connect();
    client.release();
    return true;
  } catch (error) {
    console.error('Database connection error:', error);
    return false;
  }
};

module.exports = {
  pool,
  checkConnection,
  query: (text, params) => pool.query(text, params),
};
