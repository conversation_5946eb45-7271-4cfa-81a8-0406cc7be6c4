#!/bin/bash
# Script to set up the correct Node.js version using nvm

# Check if nvm is installed
if ! command -v nvm &> /dev/null; then
    echo "nvm is not installed. Installing nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash
    
    # Source nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
fi

# Install and use the correct Node.js version from .nvmrc
echo "Installing and using Node.js version specified in .nvmrc..."
nvm install
nvm use

# Verify Node.js version
echo "Node.js version:"
node -v

# Install dependencies
echo "Installing dependencies..."
npm run install-all

echo "Setup complete! You can now run the application with 'npm run dev'"
