/**
 * <PERSON><PERSON><PERSON> to update the database schema to add image support
 */
const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'ledguyapp',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'root',
});

async function updateSchema() {
  const client = await pool.connect();
  
  try {
    console.log('Starting schema update for image support...');
    
    // Begin transaction
    await client.query('BEGIN');
    
    // Check if image_url column already exists in bulb_compatibility
    const checkImageUrlColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'bulb_compatibility' AND column_name = 'image_url'
    `);
    
    if (checkImageUrlColumn.rows.length === 0) {
      console.log('Adding image_url column to bulb_compatibility table...');
      await client.query(`
        ALTER TABLE bulb_compatibility 
        ADD COLUMN image_url VARCHAR(500)
      `);
    } else {
      console.log('image_url column already exists in bulb_compatibility table.');
    }
    
    // Check if ledguys_data column already exists in bulb_compatibility
    const checkLedguysDataColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'bulb_compatibility' AND column_name = 'ledguys_data'
    `);
    
    if (checkLedguysDataColumn.rows.length === 0) {
      console.log('Adding ledguys_data column to bulb_compatibility table...');
      await client.query(`
        ALTER TABLE bulb_compatibility 
        ADD COLUMN ledguys_data VARCHAR(500)
      `);
    } else {
      console.log('ledguys_data column already exists in bulb_compatibility table.');
    }
    
    // Check if ledguys_desc column already exists in bulb_compatibility
    const checkLedguysDescColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'bulb_compatibility' AND column_name = 'ledguys_desc'
    `);
    
    if (checkLedguysDescColumn.rows.length === 0) {
      console.log('Adding ledguys_desc column to bulb_compatibility table...');
      await client.query(`
        ALTER TABLE bulb_compatibility 
        ADD COLUMN ledguys_desc VARCHAR(500)
      `);
    } else {
      console.log('ledguys_desc column already exists in bulb_compatibility table.');
    }
    
    // Check if images table exists
    const checkImagesTable = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'images'
    `);
    
    if (checkImagesTable.rows.length === 0) {
      console.log('Creating images table...');
      await client.query(`
        CREATE TABLE images (
          id SERIAL PRIMARY KEY,
          compatibility_id INTEGER REFERENCES bulb_compatibility(id),
          filename VARCHAR(255) NOT NULL,
          original_name VARCHAR(255),
          mime_type VARCHAR(100),
          size INTEGER,
          uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(compatibility_id, filename)
        )
      `);
    } else {
      console.log('images table already exists.');
    }
    
    // Commit transaction
    await client.query('COMMIT');
    console.log('Schema update completed successfully!');
    
  } catch (error) {
    // Rollback transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error updating schema:', error);
    throw error;
  } finally {
    // Release client back to pool
    client.release();
    await pool.end();
  }
}

// Run the update
updateSchema()
  .then(() => {
    console.log('Schema update script completed.');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Schema update script failed:', err);
    process.exit(1);
  });
