/**
 * <PERSON><PERSON><PERSON> to fix the foreign key constraint in the images table
 * This script updates the foreign key to reference the new bulb_compatibility table
 */
const { pool } = require('./connection');

const fixImageForeignKey = async () => {
  const client = await pool.connect();
  
  try {
    console.log('Starting fix for images foreign key constraint...');
    
    // Begin transaction
    await client.query('BEGIN');
    
    // Check if the images table exists
    const checkImagesTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'images'
      )
    `);
    
    if (!checkImagesTable.rows[0].exists) {
      console.log('Images table does not exist, nothing to fix.');
      await client.query('COMMIT');
      return;
    }
    
    // Check if the bulb_compatibility table exists
    const checkCompatibilityTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'bulb_compatibility'
      )
    `);
    
    if (!checkCompatibilityTable.rows[0].exists) {
      console.log('Bulb compatibility table does not exist, cannot fix foreign key.');
      await client.query('COMMIT');
      return;
    }
    
    // Get the current foreign key constraint name
    const getFkConstraint = await client.query(`
      SELECT conname
      FROM pg_constraint
      WHERE conrelid = 'images'::regclass
        AND contype = 'f'
        AND confrelid = 'bulb_compatibility_old'::regclass
    `);
    
    if (getFkConstraint.rows.length === 0) {
      // Check if there's already a constraint to the new table
      const checkNewFkConstraint = await client.query(`
        SELECT conname
        FROM pg_constraint
        WHERE conrelid = 'images'::regclass
          AND contype = 'f'
          AND confrelid = 'bulb_compatibility'::regclass
      `);
      
      if (checkNewFkConstraint.rows.length > 0) {
        console.log('Foreign key constraint already points to the new table.');
        await client.query('COMMIT');
        return;
      }
      
      // No constraint found, create a new one
      console.log('No foreign key constraint found, creating a new one...');
      
      // First, drop any existing constraint
      await client.query(`
        ALTER TABLE images
        DROP CONSTRAINT IF EXISTS images_compatibility_id_fkey
      `);
      
      // Create the new constraint
      await client.query(`
        ALTER TABLE images
        ADD CONSTRAINT images_compatibility_id_fkey
        FOREIGN KEY (compatibility_id)
        REFERENCES bulb_compatibility(id)
      `);
      
      console.log('Created new foreign key constraint.');
    } else {
      // Constraint exists, drop it and create a new one
      const constraintName = getFkConstraint.rows[0].conname;
      console.log(`Found constraint: ${constraintName}`);
      
      // Drop the existing constraint
      await client.query(`
        ALTER TABLE images
        DROP CONSTRAINT ${constraintName}
      `);
      
      // Create the new constraint
      await client.query(`
        ALTER TABLE images
        ADD CONSTRAINT images_compatibility_id_fkey
        FOREIGN KEY (compatibility_id)
        REFERENCES bulb_compatibility(id)
      `);
      
      console.log('Updated foreign key constraint.');
    }
    
    // Commit transaction
    await client.query('COMMIT');
    console.log('Foreign key constraint fixed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error fixing foreign key constraint:', error);
  } finally {
    client.release();
    await pool.end();
  }
};

// Run the fix
fixImageForeignKey()
  .then(() => {
    console.log('Fix completed.');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Fix failed:', err);
    process.exit(1);
  });
