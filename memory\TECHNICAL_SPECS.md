# Technical Specifications

## Technology Stack
- Frontend: React.js
- Backend: Node.js with Express
- Database: PostgreSQL
- Hosting: Vercel

## Database Structure
Initial data comes from 4 CSV files, requiring:
- Import/seed functionality
- Optimized table structure for fast queries
- Possible denormalization for performance

## API Requirements
- Fast response times
- Connection management
- Timeout handling
- Error handling
- Future potential for subscription-based access

## Security Requirements
- Private application (not public)
- Secure database connections
- Protected routes
- Copyright protection for LED Guys

## Performance Requirements
- Quick query response times
- Efficient database design
- Minimal loading times between selections
- Optimized for sales team usage