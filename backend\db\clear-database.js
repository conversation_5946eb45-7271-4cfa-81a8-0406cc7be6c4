/**
 * <PERSON><PERSON><PERSON> to clear all data from the database tables
 */
const { pool } = require('./connection');

const clearDatabase = async () => {
  const client = await pool.connect();

  try {
    console.log('Starting database cleanup...');

    // Begin transaction
    await client.query('BEGIN');

    // Get count of records before deletion
    const countResult = await client.query('SELECT COUNT(*) FROM bulb_compatibility');
    const recordCount = parseInt(countResult.rows[0].count);
    console.log(`Found ${recordCount} records in bulb_compatibility table`);

    // Delete all records from bulb_compatibility
    console.log('Deleting all records from bulb_compatibility table...');
    await client.query('DELETE FROM bulb_compatibility');

    // Reset the sequence
    console.log('Resetting ID sequence...');
    await client.query('ALTER SEQUENCE bulb_compatibility_id_seq RESTART WITH 1');

    // Commit transaction
    await client.query('COMMIT');
    console.log(`Successfully deleted ${recordCount} records from bulb_compatibility table`);
    console.log('Database cleanup completed successfully!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error clearing database:', error);
  } finally {
    client.release();
    // Close the pool
    pool.end();
  }
};

// Run the cleanup
clearDatabase();
