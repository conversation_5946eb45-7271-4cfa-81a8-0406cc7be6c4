const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { errorHandler, notFound } = require('./middleware/errorHandler');
const apiRoutes = require('./routes/api');
const { checkConnection } = require('./db/connection');

// Load environment variables
dotenv.config();

// Initialize express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
// Increase JSON payload limit for large requests
app.use(express.json({ limit: '50mb' }));
// Increase URL-encoded payload limit for large requests
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
// Increase timeout for all requests
app.use((req, res, next) => {
  // Set timeout to 1 hour
  req.setTimeout(3600000);
  res.setTimeout(3600000);
  next();
});

// Create required directories if they don't exist
const path = require('path');
const fs = require('fs');

// Uploads directory for images
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Data directory for CSV files
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Database connection check endpoint
app.get('/api/connect', async (req, res) => {
  const isConnected = await checkConnection();
  if (isConnected) {
    res.json({ connected: true, message: 'Successfully connected to the database' });
  } else {
    res.status(500).json({ connected: false, message: 'Failed to connect to the database' });
  }
});

// API routes
app.use('/api', apiRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start the server
app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
});
