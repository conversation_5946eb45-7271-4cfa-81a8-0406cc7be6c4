const db = require('../db/connection');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');

    // Create the uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate a unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'image-' + uniqueSuffix + ext);
  }
});

// File filter to only allow image files
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

// Create the multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

/**
 * Upload an image for a compatibility record
 * @route POST /api/images/:compatibilityId
 * @param {number} compatibilityId - The ID of the compatibility record
 * @access Public
 */
const uploadImage = async (req, res) => {
  const { compatibilityId } = req.params;

  if (!compatibilityId) {
    return res.status(400).json({ message: 'Compatibility ID is required' });
  }

  try {
    // Check if the compatibility record exists
    // First try the new table name
    let compatibilityCheck = await db.query(
      'SELECT id FROM bulb_compatibility WHERE id = $1',
      [compatibilityId]
    );

    // If no record found, try the old table name
    if (compatibilityCheck.rows.length === 0) {
      console.log(`Compatibility record not found in bulb_compatibility, checking bulb_compatibility_old...`);
      compatibilityCheck = await db.query(
        'SELECT id FROM bulb_compatibility_old WHERE id = $1',
        [compatibilityId]
      );

      if (compatibilityCheck.rows.length > 0) {
        console.log(`Found compatibility record in bulb_compatibility_old, but foreign key constraint requires it in bulb_compatibility.`);
        console.log(`Please run the fix-image-fk script to update the foreign key constraint.`);
      }
    }

    if (compatibilityCheck.rows.length === 0) {
      return res.status(404).json({ message: 'Compatibility record not found' });
    }

    // File is available in req.file due to multer middleware
    if (!req.file) {
      return res.status(400).json({ message: 'No image file uploaded' });
    }

    // Save image information to the database
    const { filename, originalname, mimetype, size } = req.file;

    // Insert into images table
    const imageResult = await db.query(
      `INSERT INTO images
       (compatibility_id, filename, original_name, mime_type, size)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id`,
      [compatibilityId, filename, originalname, mimetype, size]
    );

    const imageId = imageResult.rows[0].id;

    // Update the image_url in the compatibility record
    const imageUrl = `/images/${filename}`;

    // Check which table to update (should be bulb_compatibility after running the fix script)
    const tableExists = await db.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'bulb_compatibility'
      )
    `);

    if (tableExists.rows[0].exists) {
      await db.query(
        'UPDATE bulb_compatibility SET image_url = $1 WHERE id = $2',
        [imageUrl, compatibilityId]
      );
    } else {
      // Fallback to old table name if needed
      await db.query(
        'UPDATE bulb_compatibility_old SET image_url = $1 WHERE id = $2',
        [imageUrl, compatibilityId]
      );
    }

    return res.status(201).json({
      message: 'Image uploaded successfully',
      imageId,
      imageUrl,
      filename,
      originalname,
      mimetype,
      size
    });

  } catch (error) {
    console.error('Error uploading image:', error);
    return res.status(500).json({
      message: 'Server error while uploading image',
      error: error.message
    });
  }
};

/**
 * Get an image by filename
 * @route GET /api/images/:filename
 * @param {string} filename - The filename of the image
 * @access Public
 */
const getImage = async (req, res) => {
  const { filename } = req.params;

  if (!filename) {
    return res.status(400).json({ message: 'Filename is required' });
  }

  try {
    const imagePath = path.join(__dirname, '../uploads', filename);

    // Check if the file exists
    if (!fs.existsSync(imagePath)) {
      return res.status(404).json({ message: 'Image not found' });
    }

    // Send the file
    return res.sendFile(imagePath);

  } catch (error) {
    console.error('Error retrieving image:', error);
    return res.status(500).json({
      message: 'Server error while retrieving image',
      error: error.message
    });
  }
};

/**
 * Update LED Guys data for a compatibility record
 * @route PUT /api/compatibility/:id/ledguys-data
 * @param {number} id - The ID of the compatibility record
 * @body {string} ledguysData - The LED Guys data
 * @body {string} ledguysDesc - The LED Guys description
 * @access Public
 */
const updateLedguysData = async (req, res) => {
  const { id } = req.params;
  const { ledguysData, ledguysDesc } = req.body;

  if (!id) {
    return res.status(400).json({ message: 'Compatibility ID is required' });
  }

  try {
    // Check if the compatibility record exists
    // First try the new table name
    let compatibilityCheck = await db.query(
      'SELECT id FROM bulb_compatibility WHERE id = $1',
      [id]
    );

    // If no record found, try the old table name
    if (compatibilityCheck.rows.length === 0) {
      console.log(`Compatibility record not found in bulb_compatibility, checking bulb_compatibility_old...`);
      compatibilityCheck = await db.query(
        'SELECT id FROM bulb_compatibility_old WHERE id = $1',
        [id]
      );

      if (compatibilityCheck.rows.length > 0) {
        console.log(`Found compatibility record in bulb_compatibility_old, but foreign key constraint requires it in bulb_compatibility.`);
        console.log(`Please run the fix-image-fk script to update the foreign key constraint.`);
      }
    }

    if (compatibilityCheck.rows.length === 0) {
      return res.status(404).json({ message: 'Compatibility record not found' });
    }

    // Check which table to update (should be bulb_compatibility after running the fix script)
    const tableExists = await db.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'bulb_compatibility'
      )
    `);

    // First, get the current values to handle partial updates
    const currentRecord = await db.query(
      'SELECT ledguys_data, ledguys_desc, image_url FROM bulb_compatibility WHERE id = $1',
      [id]
    );

    const currentData = currentRecord.rows[0] || { ledguys_data: null, ledguys_desc: null, image_url: null };

    // Convert empty strings to null for database storage
    // If a field is undefined, keep the current value (partial update)
    const dataValue = ledguysData === undefined ? currentData.ledguys_data :
                     (ledguysData === '' ? null : ledguysData);
    const descValue = ledguysDesc === undefined ? currentData.ledguys_desc :
                     (ledguysDesc === '' ? null : ledguysDesc);

    console.log(`Updating record ${id}:
      Current values: ledguys_data: ${currentData.ledguys_data}, ledguys_desc: ${currentData.ledguys_desc}, image_url: ${currentData.image_url}
      New values: ledguys_data: ${dataValue}, ledguys_desc: ${descValue}
    `);

    if (tableExists.rows[0].exists) {
      // Update the LED Guys data in the new table
      await db.query(
        'UPDATE bulb_compatibility SET ledguys_data = $1, ledguys_desc = $2 WHERE id = $3',
        [dataValue, descValue, id]
      );
    } else {
      // Fallback to old table name if needed
      await db.query(
        'UPDATE bulb_compatibility_old SET ledguys_data = $1, ledguys_desc = $2 WHERE id = $3',
        [dataValue, descValue, id]
      );
    }

    return res.status(200).json({
      message: 'LED Guys data updated successfully',
      id,
      ledguysData,
      ledguysDesc
    });

  } catch (error) {
    console.error('Error updating LED Guys data:', error);
    return res.status(500).json({
      message: 'Server error while updating LED Guys data',
      error: error.message
    });
  }
};

module.exports = {
  upload,
  uploadImage,
  getImage,
  updateLedguysData
};
