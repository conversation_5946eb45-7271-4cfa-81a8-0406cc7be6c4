/**
 * <PERSON><PERSON><PERSON> to update existing image URLs in the database
 * This script removes the '/api' prefix from image URLs
 */
const { pool } = require('./connection');

const updateImageUrls = async () => {
  const client = await pool.connect();
  
  try {
    console.log('Updating image URLs to remove /api prefix...');
    
    // Start a transaction
    await client.query('BEGIN');
    
    // Get all records with image URLs
    const result = await client.query(`
      SELECT id, image_url 
      FROM bulb_compatibility 
      WHERE image_url IS NOT NULL
    `);
    
    console.log(`Found ${result.rows.length} records with image URLs`);
    
    // Update each record
    let updatedCount = 0;
    for (const row of result.rows) {
      if (row.image_url && row.image_url.startsWith('/api/')) {
        const newUrl = row.image_url.replace('/api/', '/');
        
        await client.query(`
          UPDATE bulb_compatibility 
          SET image_url = $1 
          WHERE id = $2
        `, [newUrl, row.id]);
        
        updatedCount++;
      }
    }
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log(`Updated ${updatedCount} image URLs successfully!`);
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating image URLs:', error);
  } finally {
    client.release();
  }
};

// Run the update
updateImageUrls();
