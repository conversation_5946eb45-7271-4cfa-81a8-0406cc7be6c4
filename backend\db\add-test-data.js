/**
 * <PERSON><PERSON><PERSON> to add test data for 2025 without any type
 */
const { pool } = require('./connection');

const addTestData = async () => {
  const client = await pool.connect();
  
  try {
    console.log('Adding test data for 2025...');
    
    // Start a transaction
    await client.query('BEGIN');
    
    // 1. Check if Honda exists in makes table, if not add it
    let makeResult = await client.query('SELECT id FROM makes WHERE name = $1', ['Honda']);
    let makeId;
    
    if (makeResult.rows.length === 0) {
      const insertMakeResult = await client.query(
        'INSERT INTO makes (name) VALUES ($1) RETURNING id',
        ['Honda']
      );
      makeId = insertMakeResult.rows[0].id;
      console.log(`Created new make 'Honda' with ID ${makeId}`);
    } else {
      makeId = makeResult.rows[0].id;
      console.log(`Found existing make 'Honda' with ID ${makeId}`);
    }
    
    // 2. Check if Civic exists in models table, if not add it
    let modelResult = await client.query(
      'SELECT id FROM models WHERE name = $1 AND make_id = $2',
      ['Civic', makeId]
    );
    let modelId;
    
    if (modelResult.rows.length === 0) {
      const insertModelResult = await client.query(
        'INSERT INTO models (name, make_id) VALUES ($1, $2) RETURNING id',
        ['Civic', makeId]
      );
      modelId = insertModelResult.rows[0].id;
      console.log(`Created new model 'Civic' with ID ${modelId}`);
    } else {
      modelId = modelResult.rows[0].id;
      console.log(`Found existing model 'Civic' with ID ${modelId}`);
    }
    
    // 3. Add compatibility data for 2025 without a type
    // We'll use a special type ID that doesn't exist in the types table
    // This will simulate having compatibility data but no types
    
    // First, get a type ID that we can use (we'll use License Plate as an example)
    let typeResult = await client.query('SELECT id FROM types WHERE name = $1', ['License Plate']);
    let typeId;
    
    if (typeResult.rows.length === 0) {
      const insertTypeResult = await client.query(
        'INSERT INTO types (name) VALUES ($1) RETURNING id',
        ['License Plate']
      );
      typeId = insertTypeResult.rows[0].id;
      console.log(`Created new type 'License Plate' with ID ${typeId}`);
    } else {
      typeId = typeResult.rows[0].id;
      console.log(`Found existing type 'License Plate' with ID ${typeId}`);
    }
    
    // Now insert the compatibility data for 2025
    await client.query(
      `INSERT INTO bulb_compatibility 
        (year, make_id, model_id, type_id, display_code, package_id, package_name, package_code)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       ON CONFLICT (year, model_id, type_id) DO UPDATE SET
         display_code = $5,
         package_id = $6,
         package_name = $7,
         package_code = $8`,
      [
        2025,
        makeId,
        modelId,
        typeId,
        'H-CIV-LP-25',
        'PKG006',
        'License Plate LED Kit 2025',
        'LP-LED-006-25'
      ]
    );
    
    console.log('Added compatibility data for 2025 Honda Civic');
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Test data added successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error adding test data:', error);
  } finally {
    client.release();
  }
};

// Run the function and exit
addTestData()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
