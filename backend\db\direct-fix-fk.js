/**
 * Direct script to fix the foreign key constraint in the images table
 * This script uses direct SQL commands to fix the issue
 */
const { pool } = require('./connection');

const directFixFk = async () => {
  const client = await pool.connect();
  
  try {
    console.log('Starting direct fix for images foreign key constraint...');
    
    // Begin transaction
    await client.query('BEGIN');
    
    // Step 1: Drop the existing foreign key constraint
    console.log('Step 1: Dropping existing foreign key constraint...');
    await client.query(`
      ALTER TABLE images
      DROP CONSTRAINT IF EXISTS images_compatibility_id_fkey
    `);
    console.log('Existing constraint dropped.');
    
    // Step 2: Create a new foreign key constraint pointing to bulb_compatibility
    console.log('Step 2: Creating new foreign key constraint...');
    await client.query(`
      ALTER TABLE images
      ADD CONSTRAINT images_compatibility_id_fkey
      FOREIGN KEY (compatibility_id)
      REFERENCES bulb_compatibility(id)
    `);
    console.log('New constraint created.');
    
    // Step 3: Verify the constraint was created correctly
    console.log('Step 3: Verifying constraint...');
    const verifyConstraint = await client.query(`
      SELECT
        tc.constraint_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE
        tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'images'
        AND tc.constraint_name = 'images_compatibility_id_fkey'
    `);
    
    if (verifyConstraint.rows.length === 0) {
      throw new Error('Failed to create constraint');
    }
    
    console.log(`Constraint verified: ${verifyConstraint.rows[0].constraint_name} references ${verifyConstraint.rows[0].foreign_table_name}`);
    
    // Commit transaction
    await client.query('COMMIT');
    console.log('Foreign key constraint fixed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error fixing foreign key constraint:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
};

// Run the fix
directFixFk()
  .then(() => {
    console.log('Direct fix completed successfully.');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Direct fix failed:', err);
    process.exit(1);
  });
