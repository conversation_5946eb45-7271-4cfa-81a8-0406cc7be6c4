/**
 * <PERSON><PERSON><PERSON> to add test data for 2025 Tesla Model Y without any type
 * This will create a vehicle that has compatibility data but no types
 */
const { pool } = require('./connection');

const addNoTypeData = async () => {
  const client = await pool.connect();
  
  try {
    console.log('Adding test data for 2025 Tesla Model Y without any type...');
    
    // Start a transaction
    await client.query('BEGIN');
    
    // 1. Add Tesla to makes table if it doesn't exist
    let makeResult = await client.query('SELECT id FROM makes WHERE name = $1', ['Tesla']);
    let makeId;
    
    if (makeResult.rows.length === 0) {
      const insertMakeResult = await client.query(
        'INSERT INTO makes (name) VALUES ($1) RETURNING id',
        ['Tesla']
      );
      makeId = insertMakeResult.rows[0].id;
      console.log(`Created new make 'Tesla' with ID ${makeId}`);
    } else {
      makeId = makeResult.rows[0].id;
      console.log(`Found existing make 'Tesla' with ID ${makeId}`);
    }
    
    // 2. Add Model Y to models table if it doesn't exist
    let modelResult = await client.query(
      'SELECT id FROM models WHERE name = $1 AND make_id = $2',
      ['Model Y', makeId]
    );
    let modelId;
    
    if (modelResult.rows.length === 0) {
      const insertModelResult = await client.query(
        'INSERT INTO models (name, make_id) VALUES ($1, $2) RETURNING id',
        ['Model Y', makeId]
      );
      modelId = insertModelResult.rows[0].id;
      console.log(`Created new model 'Model Y' with ID ${modelId}`);
    } else {
      modelId = modelResult.rows[0].id;
      console.log(`Found existing model 'Model Y' with ID ${modelId}`);
    }
    
    // 3. Create a special "dummy" type that we'll use for the database constraint
    // but won't associate with the vehicle in the types table
    let dummyTypeResult = await client.query('SELECT id FROM types WHERE name = $1', ['_NO_TYPE_']);
    let dummyTypeId;
    
    if (dummyTypeResult.rows.length === 0) {
      const insertTypeResult = await client.query(
        'INSERT INTO types (name) VALUES ($1) RETURNING id',
        ['_NO_TYPE_']
      );
      dummyTypeId = insertTypeResult.rows[0].id;
      console.log(`Created new dummy type '_NO_TYPE_' with ID ${dummyTypeId}`);
    } else {
      dummyTypeId = dummyTypeResult.rows[0].id;
      console.log(`Found existing dummy type '_NO_TYPE_' with ID ${dummyTypeId}`);
    }
    
    // 4. Add compatibility data for 2025 Tesla Model Y with the dummy type
    // This will create a record in the compatibility table but won't show up in the types query
    await client.query(
      `INSERT INTO bulb_compatibility 
        (year, make_id, model_id, type_id, display_code, package_id, package_name, package_code)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       ON CONFLICT (year, model_id, type_id) DO UPDATE SET
         display_code = $5,
         package_id = $6,
         package_name = $7,
         package_code = $8`,
      [
        2025,
        makeId,
        modelId,
        dummyTypeId,
        'T-MY-NT-25',
        'PKG025',
        'Tesla Model Y LED Kit 2025',
        'TM-LED-025'
      ]
    );
    
    console.log('Added compatibility data for 2025 Tesla Model Y without any type');
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Test data added successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error adding test data:', error);
  } finally {
    client.release();
  }
};

// Run the function and exit
addNoTypeData()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
