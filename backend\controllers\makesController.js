const db = require('../db/connection');

/**
 * Get makes by year
 * @route GET /api/makes
 * @query year - The year to filter by
 * @access Public
 */
const getMakesByYear = async (req, res) => {
  const { year } = req.query;

  if (!year) {
    res.status(400);
    throw new Error('Year parameter is required');
  }

  try {
    // Query to get all makes that have models for the specified year
    const query = `
      SELECT DISTINCT car_make as name
      FROM bulb_compatibility
      WHERE car_year = $1
      ORDER BY car_make ASC
    `;

    const { rows } = await db.query(query, [year]);

    res.json(rows);
  } catch (error) {
    console.error('Error fetching makes:', error);
    res.status(500);
    throw new Error('Server error while fetching makes');
  }
};

module.exports = {
  getMakesByYear,
};
