const db = require('../db/connection');

/**
 * Get compatibility information by year, make, model, and type
 * @route GET /api/compatibility
 * @query year - The year to filter by
 * @query make - The make name to filter by
 * @query model - The model name to filter by
 * @query type - The bulb type to filter by (optional)
 * @access Public
 */
const getCompatibility = async (req, res) => {
  const { year, make, model, type, page = 1, limit = 30 } = req.query;
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;

  if (!year || !make || !model) {
    return res.status(400).json({ message: 'Year, make, and model parameters are required' });
  }

  try {
    let queryBase;
    let countQuery;
    let params;

    // Check if type parameter exists and is not undefined or empty
    if (type && type.trim() !== '') {
      // If type is provided, get specific compatibility information
      console.log(`Fetching compatibility for year=${year}, make=${make}, model=${model}, type=${type}, page=${pageNum}, limit=${limitNum}`);

      queryBase = `
        FROM bulb_compatibility
        WHERE car_year = $1
          AND car_make = $2
          AND car_model = $3
          AND blub_type = $4
      `;

      params = [year, make, model, type];
    } else {
      // If type is not provided, get all compatibility information for the year, make, and model
      console.log(`Fetching all compatibility for year=${year}, make=${make}, model=${model} without type filter, page=${pageNum}, limit=${limitNum}`);

      queryBase = `
        FROM bulb_compatibility
        WHERE car_year = $1
          AND car_make = $2
          AND car_model = $3
      `;

      params = [year, make, model];
    }

    // Get total count for pagination
    countQuery = `SELECT COUNT(*) as total ${queryBase}`;
    const countResult = await db.query(countQuery, params);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limitNum);

    // Get data with pagination
    const dataQuery = `
      SELECT
        id,
        car_year as year,
        car_make as make_name,
        car_model as model_name,
        sub_model,
        qualifier,
        full_name,
        bulb_id,
        bulb_name,
        blub_type as type_name,
        display_code,
        pack_id as package_id,
        pack_name as package_name,
        pack_code,
        pack_show_part,
        image_url,
        ledguys_data,
        ledguys_desc
      ${queryBase}
      ORDER BY car_year, car_make, car_model, blub_type
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const dataParams = [...params, limitNum, offset];

    const { rows } = await db.query(dataQuery, dataParams);

    console.log(`Found ${rows.length} compatibility results (page ${pageNum} of ${totalPages}, total: ${total})`);

    if (rows.length === 0 && total === 0) {
      return res.status(404).json({ message: 'No compatibility information found' });
    }

    // Return data with pagination information
    return res.json({
      results: rows,
      pagination: {
        total,
        totalPages,
        currentPage: pageNum,
        limit: limitNum,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error fetching compatibility:', error);
    return res.status(500).json({ message: 'Server error while fetching compatibility information', error: error.message });
  }
};

module.exports = {
  getCompatibility,
};
