import React, { useState, useEffect, useRef, useCallback } from 'react';
import apiService from '../services/api';
import LoadingIndicator from './LoadingIndicator';
import ErrorMessage from './ErrorMessage';
import ImageUploadModal from './ImageUploadModal';

const ResultsGrid = ({ selection }) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [expandedRow, setExpandedRow] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [showAllColumns, setShowAllColumns] = useState(false);

  // Refs for scroll-based loading
  const loadMoreRef = useRef(null);
  const resultsAreaRef = useRef(null);

  // Define fetchResults before using it in useEffect
  const fetchResults = useCallback(async (selection, page = 1, isNewSearch = false) => {
    if (isNewSearch) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }
    setError(null);

    try {
      console.log('Fetching compatibility results for:', selection, 'page:', page);

      // Check if we have a type selection or not
      let response;
      const limit = 30; // Number of results per page (30 as per requirements)

      if (selection.type) {
        // Normal case - fetch with type
        response = await apiService.getCompatibility(
          selection.year,
          selection.make,
          selection.model,
          selection.type,
          page,
          limit
        );
      } else {
        // Special case - no types available, fetch without type
        console.log('Fetching compatibility without type selection');
        response = await apiService.getCompatibility(
          selection.year,
          selection.make,
          selection.model,
          null,
          page,
          limit
        );
      }

      console.log('Compatibility results received:', response);

      // If this is a new search, replace results, otherwise append
      if (isNewSearch) {
        setResults(response.results || []);
      } else {
        setResults(prevResults => [...prevResults, ...(response.results || [])]);
      }

      // Update pagination state
      setCurrentPage(page);
      setHasMore(response.pagination?.hasNextPage || false);
      setTotalResults(response.pagination?.total || 0);
    } catch (err) {
      console.error('Error in ResultsGrid:', err);
      setError('Failed to load compatibility information. Please try again.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [/* No dependencies needed here as we want this function to be stable */]);

  // Effect to fetch results when selection changes
  useEffect(() => {
    if (selection) {
      // Reset state when selection changes
      setCurrentPage(1);
      setResults([]);
      setHasMore(false);
      setTotalResults(0);
      fetchResults(selection, 1, true);
    } else {
      setResults([]);
    }
  }, [selection, fetchResults]);

  // Handle loading more results
  const handleLoadMore = useCallback(() => {
    if (hasMore && selection && !loadingMore) {
      fetchResults(selection, currentPage + 1, false);
    }
  }, [hasMore, selection, currentPage, loadingMore, fetchResults]);

  // We're removing the automatic scroll-based loading
  // and only loading more results when the button is clicked

  const handleAddImage = (record) => {
    setSelectedRecord(record);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedRecord(null);
  };

  const handleImageUpload = async (imageFile, ledguysData, ledguysDesc) => {
    // Check if we have a selected record and at least one field to update
    if (!selectedRecord || (!imageFile && ledguysData === null && ledguysDesc === null)) return;

    try {
      setLoading(true);

      // Store the ID and current state of the record being updated
      const updatedRecordId = selectedRecord.id;

      // Check if we're removing ALL data (both fields empty and no image)
      const isRemovingAllData =
        (!imageFile &&
         (selectedRecord.ledguys_data && ledguysData === '') &&
         (selectedRecord.ledguys_desc && ledguysDesc === ''));

      // Check if we're removing some data but keeping other data
      const isPartialDataRemoval =
        (selectedRecord.ledguys_data && ledguysData === '') ||
        (selectedRecord.ledguys_desc && ledguysDesc === '');

      console.log('Data removal status:', {
        isRemovingAllData,
        isPartialDataRemoval,
        hasExistingImage: !!selectedRecord.image_url,
        willHaveImage: !!imageFile || (!!selectedRecord.image_url && !imageFile),
        willHaveLedguysData: ledguysData !== '' || (selectedRecord.ledguys_data && ledguysData !== ''),
        willHaveLedguysDesc: ledguysDesc !== '' || (selectedRecord.ledguys_desc && ledguysDesc !== '')
      });

      console.log('Updating record:', {
        id: updatedRecordId,
        hasImage: !!imageFile,
        ledguysData,
        ledguysDesc,
        isRemovingAllData,
        isPartialDataRemoval
      });

      // If an image file is provided, upload it
      if (imageFile) {
        // Create form data for the image upload
        const formData = new FormData();
        formData.append('image', imageFile);

        // Upload the image
        await apiService.uploadImage(updatedRecordId, formData);
        console.log('Image uploaded successfully');
      }

      // Always update LED Guys data (even if empty, to allow removing data)
      await apiService.updateLedguysData(updatedRecordId, ledguysData, ledguysDesc);
      console.log('LED Guys data updated successfully');

      // Close the modal first
      setShowModal(false);
      setSelectedRecord(null);

      // Force a complete refresh of the data with a new search
      if (selection) {
        console.log('Forcing complete refresh of data after update');

        try {
          // Reset state
          setResults([]);
          setCurrentPage(1);
          setHasMore(false);
          setTotalResults(0);

          // Directly fetch the updated data from the server
          const limit = 30;
          let response;

          if (selection.type) {
            response = await apiService.getCompatibility(
              selection.year,
              selection.make,
              selection.model,
              selection.type,
              1,
              limit
            );
          } else {
            response = await apiService.getCompatibility(
              selection.year,
              selection.make,
              selection.model,
              null,
              1,
              limit
            );
          }

          console.log('Fresh data received after update:', response);

          // Update state with fresh data
          setResults(response.results || []);
          setCurrentPage(1);
          setHasMore(response.pagination?.hasNextPage || false);
          setTotalResults(response.pagination?.total || 0);

          // Find the updated record in the new results
          const updatedRecord = response.results.find(r => r.id === updatedRecordId);
          console.log('Updated record in fresh data:', updatedRecord);

          // After fetching, automatically expand the row that was just updated
          // This will make the newly added data immediately visible
          // Only expand if we're not removing all data and the record has data
          if (!isRemovingAllData && updatedRecord &&
              (updatedRecord.ledguys_data || updatedRecord.ledguys_desc || updatedRecord.image_url)) {
            setTimeout(() => {
              const updatedIndex = response.results.findIndex(r => r.id === updatedRecordId);
              if (updatedIndex !== -1) {
                console.log('Expanding row at index:', updatedIndex);
                setExpandedRow(updatedIndex);
              }
            }, 200); // Slightly longer delay to ensure results are updated
          }
        } catch (refreshErr) {
          console.error('Error refreshing data after update:', refreshErr);
        }
      }
    } catch (err) {
      console.error('Error updating record:', err);
      setError('Failed to update record. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="results-area">
        <h2>Compatibility Results</h2>
        <LoadingIndicator text="Loading Results" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="results-area">
        <h2>Compatibility Results</h2>
        <ErrorMessage message={error} />
      </div>
    );
  }

  if (!selection) {
    return (
      <div className="results-area">
        <h2>Compatibility Results</h2>
        <div className="message message-info">
          Please select Year, Make, and Model to see compatibility results.
        </div>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="results-area">
        <h2>Compatibility Results</h2>
        <div className="message message-warning">
          No compatibility information found for the selected vehicle.
        </div>
      </div>
    );
  }

  const toggleDetails = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  return (
    <div className="results-area" ref={resultsAreaRef}>
      <div className="results-header">
        <h2>Compatibility Results</h2>
        <button
          className="btn btn-secondary btn-sm toggle-columns-btn"
          onClick={() => setShowAllColumns(!showAllColumns)}
        >
          <i className={`fas ${showAllColumns ? 'fa-compress-alt' : 'fa-expand-alt'}`}></i>
          {showAllColumns ? ' Show Fewer Columns' : ' Show All Columns'}
        </button>
      </div>

      <table className={`results-table ${showAllColumns ? 'show-all-columns' : ''}`}>
        <thead>
          <tr>
            <th className="always-show"></th>
            <th className="always-show">Year</th>
            <th className="always-show">Make</th>
            <th className="always-show">Model</th>
            <th className="optional-column">SubModel</th>
            <th className="optional-column">Qualifier</th>
            <th className="optional-column">Bulb ID</th>
            <th className="optional-column">Bulb Name</th>
            <th className="always-show">Bulb Type</th>
            <th className="optional-column">Display Code</th>
            <th className="optional-column">Package ID</th>
            <th className="always-show">Package Name</th>
            <th className="always-show">Package Code</th>
            <th className="optional-column">Package Show Part</th>
            <th className="always-show">Image</th>
            <th className="always-show">Actions</th>
          </tr>
        </thead>
        <tbody>
          {results.map((result, index) => (
            <React.Fragment key={index}>
              <tr>
                <td className="always-show">
                  {(result.ledguys_data || result.ledguys_desc || result.image_url) ? (
                    <button
                      className="btn btn-info btn-sm"
                      onClick={() => toggleDetails(index)}
                      aria-label={expandedRow === index ? "Hide details" : "Show details"}
                      title="View product details"
                    >
                      <i className={`fas ${expandedRow === index ? "fa-chevron-up" : "fa-chevron-down"}`}></i>
                    </button>
                  ) : (
                    <span className="no-details-indicator" title="No additional data">—</span>
                  )}
                </td>
                <td className="always-show">{result.year}</td>
                <td className="always-show">{result.make_name}</td>
                <td className="always-show">{result.model_name}</td>
                <td className="optional-column">{result.sub_model}</td>
                <td className="optional-column">{result.qualifier}</td>
                <td className="optional-column">{result.bulb_id}</td>
                <td className="optional-column">{result.bulb_name}</td>
                <td className="always-show">{result.type_name}</td>
                <td className="optional-column">{result.display_code}</td>
                <td className="optional-column">{result.package_id}</td>
                <td className="always-show">{result.package_name}</td>
                <td className="always-show">{result.pack_code}</td>
                <td className="optional-column">{result.pack_show_part}</td>
                <td className="always-show">
                  {result.image_url ? (
                    <div className="image-container" title="Product image">
                      <img
                        src={process.env.NODE_ENV === 'production'
                          ? result.image_url  // In production, use relative URL
                          : `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${result.image_url}`}
                        alt="Product"
                        className="product-image"
                        style={{ maxWidth: '80px', maxHeight: '50px' }}
                      />
                      <span className="image-indicator"><i className="fas fa-check-circle"></i></span>
                    </div>
                  ) : (
                    <span className="no-image" title="No image available">—</span>
                  )}
                </td>
                <td className="always-show">
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => handleAddImage(result)}
                    title="Add or update product information"
                  >
                    <i className="fas fa-edit"></i> Edit
                  </button>
                </td>
              </tr>
              {expandedRow === index && (
                <tr className="details-row">
                  <td colSpan="16">
                    <div className="details-content">
                      <div className="details-grid">
                        <div className="details-text">
                          {result.ledguys_data ? (
                            <div className="detail-item">
                              <strong>LED Guys Data:</strong> {result.ledguys_data}
                            </div>
                          ) : (
                            <div className="detail-item detail-item-empty">
                              <strong>LED Guys Data:</strong> <em>No data</em>
                            </div>
                          )}
                          {result.ledguys_desc ? (
                            <div className="detail-item">
                              <strong>LED Guys Description:</strong> {result.ledguys_desc}
                            </div>
                          ) : (
                            <div className="detail-item detail-item-empty">
                              <strong>LED Guys Description:</strong> <em>No description</em>
                            </div>
                          )}
                        </div>

                        {result.image_url && (
                          <div className="details-image">
                            <img
                              src={process.env.NODE_ENV === 'production'
                                ? result.image_url
                                : `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${result.image_url}`}
                              alt="Product"
                              className="product-image-large"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>

      {/* Results summary and load more button */}
      <div className="results-footer">
        {results.length > 0 && (
          <div className="results-summary">
            Showing {results.length} of {totalResults} results
          </div>
        )}

        {hasMore && (
          <div className="load-more-container" ref={loadMoreRef}>
            <button
              className="btn btn-primary load-more-btn"
              onClick={handleLoadMore}
              disabled={loadingMore}
            >
              {loadingMore ? (
                <>
                  <LoadingIndicator text="" />
                  <span>Loading more results...</span>
                </>
              ) : (
                <>
                  <i className="fas fa-sync-alt"></i>
                  <span>Load More Results</span>
                  <span className="load-more-count">+30</span>
                </>
              )}
            </button>
          </div>
        )}

        {loadingMore && (
          <div className="loading-more-indicator">
            <LoadingIndicator text="Loading more results" />
          </div>
        )}
      </div>

      {showModal && (
        <ImageUploadModal
          show={showModal}
          onClose={handleCloseModal}
          onUpload={handleImageUpload}
          record={selectedRecord}
        />
      )}
    </div>
  );
};

export default ResultsGrid;
