# LED GUYS - Vehicle LED Bulb Lookup Tool

A web-based application for LED Guys sales team to quickly look up LED bulb compatibility for various vehicles. The tool helps salespeople find the right LED bulb specifications based on vehicle year, make, model, and type.

## Project Structure

- `backend/` - Node.js/Express backend
- `frontend/` - React.js frontend
- `docs/` - Project documentation
- `memory/` - Project requirements and specifications
- `schema.sql` - Database schema

## Prerequisites

- Node.js (v20 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## Setup Instructions

### Database Setup

1. Create a PostgreSQL database:

```sql
CREATE DATABASE ledguyapp;
```

2. Run the schema.sql script to create tables:

```bash
psql -d ledguyapp -f schema.sql
```

3. Place your CSV data files in the `backend/data/` directory:
   - makes.csv
   - models.csv
   - types.csv
   - compatibility.csv

### Backend Setup

1. Navigate to the backend directory:

```bash
cd backend
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file with your database configuration:

```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ledguyapp
DB_USER=your_db_user
DB_PASSWORD=your_db_password
PORT=5000
NODE_ENV=development
CONNECTION_TIMEOUT=300000
```

4. Import data from CSV files:

```bash
node db/import-data.js
```

5. Start the backend server:

```bash
npm start
```

The backend will be running at http://localhost:5000

### Frontend Setup

1. Navigate to the frontend directory:

```bash
cd frontend
```

2. Install dependencies:

```bash
npm install
```

3. Start the frontend development server:

```bash
npm start
```

The frontend will be running at http://localhost:3000

## Features

- Database connection management with automatic 5-minute inactivity timeout
- Cascading selection flow (Year → Make → Model → Type)
- Results display in grid format with 30 items per page
- "Load More" functionality with auto-loading on scroll
- Loading states with animated indicators
- Error handling
- Responsive design

## Technology Stack

- Frontend: React.js
- Backend: Node.js with Express
- Database: PostgreSQL
- Hosting: Vercel (planned)

## API Endpoints

- `GET /api/connect` - Check database connection
- `GET /api/years` - Get all available years dynamically from the database
- `GET /api/makes?year=:year` - Get makes by year
- `GET /api/models?year=:year&make=:make` - Get models by year and make
- `GET /api/types?year=:year&make=:make&model=:model` - Get types by year, make, and model
- `GET /api/compatibility?year=:year&make=:make&model=:model&type=:type` - Get compatibility information

## License

Copyright © LED Guys. All rights reserved.
