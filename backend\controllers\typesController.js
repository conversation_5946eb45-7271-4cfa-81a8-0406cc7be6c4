const db = require('../db/connection');

/**
 * Get types by year, make, and model
 * @route GET /api/types
 * @query year - The year to filter by
 * @query make - The make ID to filter by
 * @query model - The model ID to filter by
 * @access Public
 */
const getTypesByYearMakeModel = async (req, res) => {
  const { year, make, model } = req.query;

  if (!year || !make || !model) {
    return res.status(400).json({ message: 'Year, make, and model parameters are required' });
  }

  try {
    // First check if there's compatibility data for this year/make/model
    const compatibilityQuery = `
      SELECT COUNT(*) as count
      FROM bulb_compatibility
      WHERE car_year = $1 AND car_make = $2 AND car_model = $3
    `;

    const compatibilityResult = await db.query(compatibilityQuery, [year, make, model]);
    const hasCompatibilityData = parseInt(compatibilityResult.rows[0].count) > 0;

    // Query to get all types for the specified year, make, and model
    // Exclude any empty or null types
    const typesQuery = `
      SELECT DISTINCT blub_type as name
      FROM bulb_compatibility
      WHERE car_year = $1
        AND car_make = $2
        AND car_model = $3
        AND blub_type IS NOT NULL
        AND blub_type != ''
      ORDER BY blub_type ASC
    `;

    const typesResult = await db.query(typesQuery, [year, make, model]);

    // Return both the types and a flag indicating if there's compatibility data
    // but no types (which means we should show results without requiring type selection)
    return res.json({
      types: typesResult.rows,
      hasCompatibilityData: hasCompatibilityData,
      noTypesAvailable: hasCompatibilityData && typesResult.rows.length === 0
    });
  } catch (error) {
    console.error('Error fetching types:', error);
    return res.status(500).json({ message: 'Server error while fetching types' });
  }
};

module.exports = {
  getTypesByYearMakeModel,
};
