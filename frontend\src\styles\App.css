/* Main App Styles */
:root {
  --primary-color: #0056b3;
  --secondary-color: #003366;
  --accent-color: #00a8e8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}

body {
  font-family: 'Roboto', sans-serif;
  margin: 0;
  padding: 0;
  background: #f5f7fa;
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header Styles */
.header {
  background-color: white;
  padding: 0;
  text-align: center;
  border: none;
  height: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  width: 100%;
  max-width: 100%;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0;
  background-color: white;
  overflow: hidden;
}

.logo {
  width: 100%;
  height: auto;
  display: block;
}

/* Selection Area Styles */
.selection-area {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0; /* Stick to the top of the viewport */
  z-index: 100; /* Ensures it appears above other content */
  transition: all 0.3s ease; /* Smooth transition for shadow effect */
}

/* Class for when the selection area is in sticky state */
.selection-area.is-sticky {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 100%;
  left: 0;
  right: 0;
  margin: 0;
  border-radius: 0;
  padding-left: calc((100% - 1200px) / 2 + 20px);
  padding-right: calc((100% - 1200px) / 2 + 20px);
  border-bottom: 2px solid var(--primary-color);
}

/* Selection header with progress indicator */
.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.selection-area h2 {
  color: var(--secondary-color);
  margin: 0;
  font-size: 1.3rem;
  padding-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.selection-progress {
  display: flex;
  align-items: center;
  gap: 5px;
}

.progress-step {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  transition: all 0.3s ease;
}

.progress-step.complete {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-line {
  height: 2px;
  width: 20px;
  background-color: #ddd;
}

.selection-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  align-items: flex-end;
}

.selection-group {
  flex: 1;
  min-width: 200px;
  position: relative;
  z-index: 1;
}

.selection-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--secondary-color);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.optional-label {
  font-size: 0.7rem;
  font-weight: normal;
  color: #666;
  text-transform: none;
  letter-spacing: normal;
  margin-left: 5px;
}

/* Original dropdown styles - kept for non-React-Select elements */
.selection-dropdown {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: var(--light-color);
  font-size: 16px;
}

.selection-dropdown:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

/* React Select specific overrides */
.selection-group .react-select-container {
  font-size: 16px;
}

/* Ensure dropdowns appear above other elements */
.selection-group .react-select__menu {
  z-index: 999;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #bd2130;
}

.btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

/* Results Grid Styles */
.results-area {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow-x: auto; /* Enable horizontal scrolling for wide tables */
}

.results-area h2 {
  color: var(--secondary-color);
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.results-table th,
.results-table td {
  padding: 8px 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  white-space: nowrap; /* Prevent text wrapping */
}

.results-table th {
  background-color: var(--secondary-color);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
}

.results-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.results-table tr:hover {
  background-color: #f0f7ff;
}

/* Optional columns that can be toggled */
.results-table .optional-column {
  display: none;
}

.results-table.show-all-columns .optional-column {
  display: table-cell;
}

/* Results header with toggle button */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.toggle-columns-btn {
  font-size: 0.8rem;
  padding: 6px 12px;
}

.no-image, .no-details-indicator {
  color: #999;
  font-style: italic;
}

.image-container {
  position: relative;
  display: inline-block;
}

.image-indicator {
  position: absolute;
  bottom: -5px;
  right: -5px;
  color: #27ae60;
  font-size: 0.9rem;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Loading Indicator Styles */
.loading {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 20px;
}

.loading div {
  position: absolute;
  top: 8px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: var(--primary-color);
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading div:nth-child(1) {
  left: 8px;
  animation: loading1 0.6s infinite;
}

.loading div:nth-child(2) {
  left: 8px;
  animation: loading2 0.6s infinite;
}

.loading div:nth-child(3) {
  left: 32px;
  animation: loading2 0.6s infinite;
}

.loading div:nth-child(4) {
  left: 56px;
  animation: loading3 0.6s infinite;
}

@keyframes loading1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loading2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}

@keyframes loading3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

/* Message Styles */
.message {
  padding: 12px 15px;
  margin: 15px 0;
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

.message-error {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.message-success {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.message-warning {
  background-color: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffc107;
}

/* Connection Manager Styles */
.connection-manager {
  margin: 10px 0;
}

.connection-status {
  display: flex;
  justify-content: flex-end;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-indicator.connected {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-indicator.disconnected {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.status-indicator i {
  font-size: 1rem;
}

.status-indicator span {
  font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ddd;
}

.modal-header h3 {
  margin: 0;
  color: var(--secondary-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-note {
  margin: 10px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #666;
}

.form-note p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.form-note p.warning {
  color: #e67e22;
  margin-top: 8px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: var(--secondary-color);
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 14px;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.product-image {
  display: block;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 3px;
  background-color: white;
}

/* Details Row Styles */
.details-row {
  background-color: #f8f9fa;
}

.details-content {
  padding: 15px;
}

.details-grid {
  display: flex;
  gap: 20px;
}

.details-text {
  flex: 1;
}

.details-image {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.product-image-large {
  max-width: 200px;
  max-height: 150px;
  object-fit: contain;
}

@media (max-width: 768px) {
  .details-grid {
    flex-direction: column;
  }

  .details-image {
    margin-top: 15px;
  }
}

.detail-item {
  margin-bottom: 10px;
  line-height: 1.5;
}

.detail-item-empty {
  color: #999;
  font-style: italic;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.btn-info {
  background-color: var(--info-color);
  color: white;
}

.btn-info:hover {
  background-color: #138496;
}

/* CSV Upload Styles */
.csv-upload-area {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.upload-section {
  margin-top: 20px;
}

.format-info {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.format-info h3 {
  margin-top: 0;
  color: var(--secondary-color);
}

.format-info h4 {
  margin-top: 15px;
  color: var(--secondary-color);
}

.format-note {
  margin-top: 15px;
  font-style: italic;
}

.sample-data {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
  font-size: 14px;
  white-space: pre-wrap;
}

.file-input {
  display: block;
  width: 100%;
  padding: 8px;
  margin-top: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.file-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.upload-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.progress-container {
  margin-top: 20px;
}

.progress-label {
  margin-bottom: 5px;
  font-weight: bold;
}

.progress-bar {
  height: 20px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.importing-indicator {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  text-align: center;
}

.import-details {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.import-log {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
  font-size: 14px;
  white-space: pre-wrap;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #218838;
}

/* View Toggle Styles */
.view-toggle {
  display: flex;
  gap: 2px;
  margin: 20px 0;
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-toggle .btn {
  flex: 1;
  text-align: center;
  padding: 12px 20px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: none;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.view-toggle .btn-primary {
  background-color: white;
  color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.view-toggle .btn-secondary {
  background-color: transparent;
  color: #555;
}

.view-toggle .btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.5);
  color: var(--primary-color);
}

/* Results Footer Styles */
.results-footer {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.results-summary {
  font-size: 16px;
  color: var(--secondary-color);
  font-weight: 500;
  margin-bottom: 15px;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  padding: 10px;
}

.load-more-btn {
  min-width: 220px;
  padding: 12px 24px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.load-more-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.load-more-count {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 2px 8px;
  font-size: 14px;
  font-weight: bold;
}

.loading-more-indicator {
  text-align: center;
  margin: 20px 0;
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .selection-row {
    flex-direction: column;
  }

  .selection-group {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    max-height: 80vh;
  }

  /* Adjust sticky selection area padding for smaller screens */
  .selection-area.is-sticky {
    padding-left: 20px;
    padding-right: 20px;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-info {
    text-align: center;
  }
}

/* Footer Styles */
.footer {
  background-color: var(--secondary-color);
  color: white;
  padding: 30px 0;
  margin-top: 40px;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-logo {
  flex: 0 0 auto;
}

.footer-logo-img {
  height: 40px;
  width: auto;
}

.footer-info {
  flex: 1 1 auto;
  text-align: right;
}

.footer-info p {
  margin: 5px 0;
}

.footer-tagline {
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}
