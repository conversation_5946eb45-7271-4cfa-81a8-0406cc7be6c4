# Database Schema Design

## Core Tables

### Makes Table
- Primary key: `id` (SERIAL)
- `name` VARCHA<PERSON>(100), UNIQUE
- Stores vehicle manufacturers (e.g., Acura, BMW)

### Models Table
- Primary key: `id` (SERIAL)
- Foreign key: `make_id` references Makes
- `name` VARCHAR(100)
- Composite UNIQUE constraint on (make_id, name)
- Stores vehicle models (e.g., RDX, MDX)

### Types Table
- Primary key: `id` (SERIAL)
- `name` VARCHAR(100), UNIQUE
- Stores LED application types (e.g., Stepwell)

### Bulb Compatibility Table
- Primary key: `id` (SERIAL)
- `year` INTEGER (2019-2024)
- Foreign keys:
  - `make_id` references Makes
  - `model_id` references Models
  - `type_id` references Types
- Product information:
  - `display_code` VARCHAR(50)
  - `package_id` VARCHAR(50)
  - `package_name` VARCHAR(200)
  - `package_code` VARCHAR(50)
- Composite UNIQUE constraint on (year, model_id, type_id)

## Schema Considerations
1. Normalized Design
   - Reduces data redundancy
   - Maintains data integrity
   - Efficient updates and maintenance

2. Performance Optimizations
   - Strategic indexing on frequently queried columns
   - Composite unique constraints for data validity
   - Foreign key constraints for referential integrity