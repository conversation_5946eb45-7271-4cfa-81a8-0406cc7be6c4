/**
 * <PERSON><PERSON><PERSON> to create the images table for image uploads
 */
const { pool } = require('./connection');

const createImagesTable = async () => {
  const client = await pool.connect();

  try {
    console.log('Starting creation of images table...');

    // Begin transaction
    await client.query('BEGIN');

    // Check if images table already exists
    const checkTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'images'
      )
    `);

    if (checkTable.rows[0].exists) {
      console.log('Images table already exists. No action needed.');
    } else {
      console.log('Creating images table...');
      await client.query(`
        CREATE TABLE images (
          id SERIAL PRIMARY KEY,
          compatibility_id INTEGER REFERENCES bulb_compatibility(id),
          filename VARCHAR(255) NOT NULL,
          original_name VARCHAR(255),
          mime_type VARCHAR(100),
          size INTEGER,
          uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(compatibility_id, filename)
        )
      `);
      console.log('Images table created successfully!');
    }

    // Commit transaction
    await client.query('COMMIT');
    console.log('Operation completed successfully!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating images table:', error);
  } finally {
    client.release();
    // Close the pool
    pool.end();
  }
};

// Run the function
createImagesTable();
