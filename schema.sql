
-- Main compatibility table
CREATE TABLE bulb_compatibility (
    id SERIAL PRIMARY KEY,
    car_year INTEGER,           -- Year
    car_make VARCHAR(50),       -- Make
    car_model VARCHAR(50),      -- Model
    sub_model VARCHAR(100),     -- SubModel
    qualifier VARCHAR(100),     -- Qualifier
    full_name VARCHAR(255),     -- FullName
    bulb_id VARCHAR(50),        -- Id
    bulb_name VARCHAR(100),     -- Name
    blub_type VARCHAR(50),      -- Type
    display_code VARCHAR(50),   -- Displaycode
    pack_id VARCHAR(50),        -- PackageId
    pack_name VARCHAR(250),     -- PackageName
    pack_code VARCHAR(50),      -- PackageCode
    pack_show_part VARCHAR(100), -- PackageShowPart
    ledguys_data VARCHAR(500),  -- Custom LED Guys data
    ledguys_desc VARCHAR(500),  -- Custom LED Guys description
    image_url VARCHAR(500)      -- URL to product image
);


-- Table for storing image information
CREATE TABLE images (
    id SERIAL PRIMARY KEY,
    compatibility_id INTEGER REFERENCES bulb_compatibility(id),
    filename VARCHAR(255) NOT NULL,
    original_name VA<PERSON><PERSON><PERSON>(255),
    mime_type VARCHAR(100),
    size INTEGER,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(compatibility_id, filename)
);
