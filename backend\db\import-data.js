/**
 * Data Import Script for LED Guys Bulb Lookup Tool
 *
 * This script imports data from CSV files into the PostgreSQL database.
 * It assumes the following CSV files exist in the data directory:
 * - makes.csv
 * - models.csv
 * - types.csv
 * - compatibility.csv
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const { parse } = require('csv-parse/sync'); // Using sync parser
require('dotenv').config();

// Create a connection pool
const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

// Path to CSV files
const dataDir = path.join(__dirname, '../data');

// Parse CSV file
const parseCSV = (filePath) => {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  return parse(fileContent, { columns: true, trim: true });
};

// Import makes
const importMakes = async (client) => {
  console.log('Importing makes...');

  const makesFile = path.join(dataDir, 'makes.csv');
  const makeData = parseCSV(makesFile);

  for (const make of makeData) {
    await client.query(
      'INSERT INTO makes (name) VALUES ($1) ON CONFLICT (name) DO NOTHING',
      [make.name]
    );
  }

  console.log(`Imported ${makeData.length} makes`);
  return makeData.length;
};

// Import models
const importModels = async (client) => {
  console.log('Importing models...');

  const modelsFile = path.join(dataDir, 'models.csv');
  const modelData = parseCSV(modelsFile);
  let importedCount = 0;

  for (const model of modelData) {
    // Get make_id from make name
    const makeResult = await client.query(
      'SELECT id FROM makes WHERE name = $1',
      [model.make_name]
    );

    if (makeResult.rows.length > 0) {
      const makeId = makeResult.rows[0].id;

      await client.query(
        'INSERT INTO models (make_id, name) VALUES ($1, $2) ON CONFLICT (make_id, name) DO NOTHING',
        [makeId, model.name]
      );
      importedCount++;
    } else {
      console.warn(`Make not found: ${model.make_name}`);
    }
  }

  console.log(`Imported ${importedCount} models`);
  return importedCount;
};

// Import types
const importTypes = async (client) => {
  console.log('Importing types...');

  const typesFile = path.join(dataDir, 'types.csv');
  const typeData = parseCSV(typesFile);

  for (const type of typeData) {
    await client.query(
      'INSERT INTO types (name) VALUES ($1) ON CONFLICT (name) DO NOTHING',
      [type.name]
    );
  }

  console.log(`Imported ${typeData.length} types`);
  return typeData.length;
};

// Import compatibility data
const importCompatibility = async (client) => {
  console.log('Importing compatibility data...');

  const compatibilityFile = path.join(dataDir, 'compatibility.csv');
  const compatibilityData = parseCSV(compatibilityFile);
  let importedCount = 0;

  for (const item of compatibilityData) {
    try {
      // Map CSV fields to database fields
      const carYear = parseInt(item.car_year || item.year);
      const carMake = item.car_make || item.make_name;
      const carModel = item.car_model || item.model_name;
      const blubType = item.blub_type || item.type_name;
      const disType = item.dis_type || '';
      const disCode = item.dis_code || item.display_code;
      const packId = item.pack_id || item.package_id;
      const packName = item.pack_name || item.package_name;
      const productvalue = item.productvalue || item.package_code;

      // Insert compatibility data directly with the new schema
      await client.query(
        `INSERT INTO bulb_compatibility
          (car_year, car_make, car_model, blub_type, dis_type, dis_code, pack_id, pack_name, productvalue)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
         ON CONFLICT DO NOTHING`,
        [
          carYear,
          carMake,
          carModel,
          blubType,
          disType,
          disCode,
          packId,
          packName,
          productvalue
        ]
      );
      importedCount++;
    } catch (err) {
      console.warn(`Error importing record: ${err.message}`);
      console.warn('Record data:', JSON.stringify(item));
    }
  }

  console.log(`Imported ${importedCount} compatibility records`);
  return importedCount;
};

// Run the import process
const runImport = async () => {
  let client = null;

  try {
    // Create data directory if it doesn't exist
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Check if CSV files exist
    const requiredFiles = ['makes.csv', 'models.csv', 'types.csv', 'compatibility.csv'];
    const missingFiles = requiredFiles.filter(file => !fs.existsSync(path.join(dataDir, file)));

    if (missingFiles.length > 0) {
      console.error(`Missing CSV files: ${missingFiles.join(', ')}`);
      console.error(`Please place the CSV files in the ${dataDir} directory`);
      process.exit(1);
    }

    // Get a client from the pool
    client = await pool.connect();

    // Start transaction
    await client.query('BEGIN');

    // Import data in sequence
    await importMakes(client);
    await importModels(client);
    await importTypes(client);
    await importCompatibility(client);

    // Commit transaction
    await client.query('COMMIT');

    console.log('Import completed successfully');
  } catch (err) {
    // Rollback transaction on error
    if (client) {
      await client.query('ROLLBACK');
    }
    console.error('Import failed:', err);
  } finally {
    // Release client back to the pool
    if (client) {
      client.release();
    }

    // Close the pool
    await pool.end();
  }
};

// Run the import
runImport();
