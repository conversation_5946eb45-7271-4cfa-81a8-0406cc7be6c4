import React, { useState, useEffect } from 'react';
import apiService from '../services/api';
import LoadingIndicator from './LoadingIndicator';
import ErrorMessage from './ErrorMessage';

const ConnectionManager = ({ onConnectionChange }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState(null);

  // Handle connection timeout
  useEffect(() => {
    const handleTimeout = () => {
      setIsConnected(false);
      setError('Database connection timed out. Please reconnect.');
      onConnectionChange(false);
    };

    window.addEventListener('db-connection-timeout', handleTimeout);

    return () => {
      window.removeEventListener('db-connection-timeout', handleTimeout);
      // Disconnect when component unmounts
      apiService.disconnect();
    };
  }, [onConnectionChange]);

  const handleConnect = async () => {
    setIsConnecting(true);
    setError(null);

    try {
      await apiService.connect();
      setIsConnected(true);
      onConnectionChange(true);
    } catch (err) {
      setError('Failed to connect to the database. Please try again.');
      onConnectionChange(false);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    apiService.disconnect();
    setIsConnected(false);
    onConnectionChange(false);
    setError(null);
  };

  return (
    <div className="connection-manager">
      <div className="connection-status">
        {isConnected ? (
          <div className="status-indicator connected">
            <i className="fas fa-database"></i>
            <span>Database Connected</span>
            <button
              className="btn btn-danger btn-sm"
              onClick={handleDisconnect}
              title="Disconnect from database"
            >
              <i className="fas fa-unlink"></i>
            </button>
          </div>
        ) : (
          <div className="status-indicator disconnected">
            <i className="fas fa-database"></i>
            <span>Database Disconnected</span>
            <button
              className="btn btn-primary btn-sm"
              onClick={handleConnect}
              disabled={isConnecting}
              title="Connect to database"
            >
              {isConnecting ? (
                <><LoadingIndicator text="" /> Connecting...</>
              ) : (
                <><i className="fas fa-link"></i> Connect</>
              )}
            </button>
          </div>
        )}
      </div>

      {error && <ErrorMessage message={error} />}
    </div>
  );
};

export default ConnectionManager;
