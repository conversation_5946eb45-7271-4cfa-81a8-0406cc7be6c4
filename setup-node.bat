@echo off
echo This script will help you set up the correct Node.js version for this project.

echo Checking for Node.js installation...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Node.js is not installed or not in your PATH.
    echo Please download and install Node.js v20.12.2 from:
    echo https://nodejs.org/dist/v20.12.2/node-v20.12.2-x64.msi
    echo After installation, run this script again.
    pause
    exit /b
)

echo Checking Node.js version...
for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i
echo Current Node.js version: %NODE_VERSION%

echo Required Node.js version: v20.12.2 or higher
echo If your version is lower, please download and install the latest LTS version from:
echo https://nodejs.org/

echo Installing dependencies...
call npm run install-all

echo Setup complete! You can now run the application with 'npm run dev'
pause
