import React, { useState } from 'react';

const ImageUploadModal = ({ show, onClose, onUpload, record }) => {
  const [imageFile, setImageFile] = useState(null);
  const [ledguysData, setLedguysData] = useState(record?.ledguys_data || '');
  const [ledguysDesc, setLedguysDesc] = useState(record?.ledguys_desc || '');
  const [previewUrl, setPreviewUrl] = useState(null);

  if (!show) {
    return null;
  }

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);

      // Create a preview URL for the image
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpload(imageFile, ledguysData, ledguysDesc);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add/Update Product Information</h3>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="image">Product Image: <span className="optional-label">(Optional)</span></label>
              <input
                type="file"
                id="image"
                accept="image/*"
                onChange={handleFileChange}
                className="form-control"
              />

              {previewUrl && (
                <div className="image-preview">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    style={{ maxWidth: '100%', maxHeight: '200px', marginTop: '10px' }}
                  />
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="ledguysData">LED Guys Data: <span className="optional-label">(Optional)</span></label>
              <input
                type="text"
                id="ledguysData"
                value={ledguysData}
                onChange={(e) => setLedguysData(e.target.value)}
                className="form-control"
                placeholder="Enter LED Guys specific data"
              />
            </div>

            <div className="form-group">
              <label htmlFor="ledguysDesc">LED Guys Description: <span className="optional-label">(Optional)</span></label>
              <textarea
                id="ledguysDesc"
                value={ledguysDesc}
                onChange={(e) => setLedguysDesc(e.target.value)}
                className="form-control"
                placeholder="Enter LED Guys description"
                rows="3"
              />
            </div>

            <div className="form-note">
              <p><i className="fas fa-info-circle"></i> At least one field must be provided to save changes.</p>
              {(record?.ledguys_data && ledguysData === '') || (record?.ledguys_desc && ledguysDesc === '') ? (
                <p className="warning"><i className="fas fa-exclamation-triangle"></i> Warning: Empty fields will remove existing data.</p>
              ) : null}
            </div>

            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={!imageFile && !ledguysData && !ledguysDesc}
              >
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ImageUploadModal;
