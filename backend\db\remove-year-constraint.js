/**
 * <PERSON><PERSON><PERSON> to completely remove the year constraint from the bulb_compatibility table
 */
const { pool } = require('./connection');

const removeYearConstraint = async () => {
  const client = await pool.connect();

  try {
    console.log('Starting removal of year constraint...');

    // Begin transaction
    await client.query('BEGIN');

    // Check if the constraint exists
    const checkConstraint = await client.query(`
      SELECT conname
      FROM pg_constraint
      WHERE conrelid = 'bulb_compatibility'::regclass
      AND conname LIKE '%year%check'
    `);

    if (checkConstraint.rows.length > 0) {
      const constraintName = checkConstraint.rows[0].conname;
      console.log(`Found constraint: ${constraintName}`);

      // Drop the existing constraint
      console.log('Dropping year constraint...');
      await client.query(`
        ALTER TABLE bulb_compatibility
        DROP CONSTRAINT ${constraintName}
      `);

      console.log('Year constraint removed successfully!');
    } else {
      console.log('No year constraint found. Nothing to remove.');
    }

    // Commit transaction
    await client.query('COMMIT');
    console.log('Constraint removal completed successfully!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error removing year constraint:', error);
  } finally {
    client.release();
    // Close the pool
    pool.end();
  }
};

// Run the update
removeYearConstraint();
