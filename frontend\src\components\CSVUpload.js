import React, { useState } from 'react';
import apiService from '../services/api';
import LoadingIndicator from './LoadingIndicator';
import ErrorMessage from './ErrorMessage';

const CSVUpload = () => {
  // File states - fixed to compatibility as it's the only option
  const selectedFileType = 'compatibility';
  const [selectedFile, setSelectedFile] = useState(null);

  // Upload states
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);

  // Import states
  const [isImporting, setIsImporting] = useState(false);
  const [importComplete, setImportComplete] = useState(false);
  const [importDetails, setImportDetails] = useState('');

  // Error state
  const [error, setError] = useState(null);

  // Message state
  const [message, setMessage] = useState(null);

  // Format information loading has been removed

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        setSelectedFile(file);
        setError(null);
      } else {
        setSelectedFile(null);
        setError('Please select a CSV file.');
      }
    }
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!selectedFileType || !selectedFile) {
      setError('Please select a file type and a CSV file.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);
    setMessage(null);

    try {
      // Upload the file
      await apiService.uploadCSV(selectedFileType, selectedFile, (progress) => {
        setUploadProgress(progress);
      });

      setUploadComplete(true);
      setMessage(`${selectedFileType}.csv uploaded successfully. You can now import the data.`);
    } catch (err) {
      console.error('Error uploading file:', err);
      setError('Failed to upload file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle data import
  const handleImport = async () => {
    if (!uploadComplete) {
      setError('Please upload a file first.');
      return;
    }

    setIsImporting(true);
    setError(null);
    setMessage(null);

    try {
      // Import the data with the selected file type
      const response = await apiService.importCSV(selectedFileType);

      setImportComplete(true);
      setImportDetails(response.details || '');

      // Show appropriate message based on the response
      if (response.results) {
        // Multiple file types were imported
        const successCount = Object.values(response.results).filter(r => !r.error).length;
        const totalCount = Object.keys(response.results).length;

        if (successCount === totalCount) {
          setMessage('All data imported successfully!');
        } else {
          setMessage(`${successCount} of ${totalCount} file types imported successfully. See details below.`);
        }
      } else {
        // Single file type was imported
        setMessage(`${selectedFileType} data imported successfully! Processed ${response.processed} records, inserted ${response.inserted} new records.`);
      }
    } catch (err) {
      console.error('Error importing data:', err);

      // Extract more detailed error message if available
      const errorMessage = err.response?.data?.error || err.message || 'Failed to import data';
      setError(`Import failed: ${errorMessage}. Please try again.`);
    } finally {
      setIsImporting(false);
    }
  };

  // Format information has been removed as requested

  return (
    <div className="csv-upload-area">
      <h2>Compatibility Data Upload</h2>

      {error && <ErrorMessage message={error} />}

      {message && (
        <div className="message message-success">
          {message}
        </div>
      )}

      <div className="upload-section">
        <p>Use this form to upload compatibility data in CSV format.</p>

        {selectedFileType && (
          <>
            <div className="form-group">
              <label htmlFor="csvFile">Select CSV File</label>
              <input
                type="file"
                id="csvFile"
                accept=".csv"
                onChange={handleFileChange}
                className="file-input"
                disabled={isUploading || isImporting}
              />
              {selectedFile && (
                <div className="file-info">
                  <p>Selected file: <strong>{selectedFile.name}</strong></p>
                  <p>Size: <strong>{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</strong></p>
                </div>
              )}
            </div>

            <div className="upload-actions">
              <button
                className="btn btn-primary"
                onClick={handleUpload}
                disabled={!selectedFile || isUploading || isImporting}
              >
                {isUploading ? 'Uploading...' : 'Upload File'}
              </button>

              {uploadComplete && (
                <button
                  className="btn btn-success"
                  onClick={handleImport}
                  disabled={isImporting}
                >
                  {isImporting ? 'Importing...' : 'Import Data'}
                </button>
              )}
            </div>

            {isUploading && (
              <div className="progress-container">
                <div className="progress-label">Uploading: {uploadProgress}%</div>
                <div className="progress-bar">
                  <div
                    className="progress-bar-fill"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {isImporting && (
              <div className="importing-indicator">
                <LoadingIndicator text="Importing Data" />
                <p>This may take several minutes for large files...</p>
              </div>
            )}

            {importComplete && importDetails && (
              <div className="import-details">
                <h3>Import Results</h3>
                <pre className="import-log">{importDetails}</pre>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CSVUpload;
