# PostgreSQL Setup Guide for LED Guys Bulb Lookup Tool

This guide provides step-by-step instructions for setting up PostgreSQL for the LED Guys Bulb Lookup Tool.

## 1. Install PostgreSQL

### Windows

1. Download the PostgreSQL installer from the [official website](https://www.postgresql.org/download/windows/).
2. Run the installer and follow the installation wizard.
3. When prompted, set a password for the default 'postgres' user.
4. Keep the default port (5432).
5. Complete the installation.

### macOS

Using Homebrew:
```bash
brew install postgresql
brew services start postgresql
```

### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 2. Create a Database

### Using pgAdmin (GUI)

1. Open pgAdmin (installed with PostgreSQL).
2. Connect to your PostgreSQL server.
3. Right-click on "Databases" and select "Create" > "Database".
4. Enter "ledguyapp" as the database name and click "Save".

### Using Command Line

1. Open a terminal or command prompt.
2. Connect to PostgreSQL as the postgres user:

   **Windows:**
   ```bash
   psql -U postgres
   ```

   **macOS/Linux:**
   ```bash
   sudo -u postgres psql
   ```

3. Create the database:
   ```sql
   CREATE DATABASE ledguyapp;
   ```

4. Exit the PostgreSQL prompt:
   ```sql
   \q
   ```

## 3. Create Database Tables

1. Navigate to the project directory.
2. Run the schema.sql script:

   **Windows:**
   ```bash
   psql -U postgres -d ledguyapp -f schema.sql
   ```

   **macOS/Linux:**
   ```bash
   sudo -u postgres psql -d ledguyapp -f schema.sql
   ```

## 4. Configure Database Connection

1. Open the `.env` file in the `backend` directory.
2. Update the database connection settings:

   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=ledguyapp
   DB_USER=postgres
   DB_PASSWORD=your_postgres_password
   ```

   Replace `your_postgres_password` with the password you set during PostgreSQL installation.

## 5. Import Sample Data

1. Make sure the CSV files are in the `backend/data` directory:
   - makes.csv
   - models.csv
   - types.csv
   - compatibility.csv

2. Navigate to the backend directory:
   ```bash
   cd backend
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Run the import script:
   ```bash
   npm run import-data
   ```

## 6. Verify Database Setup

1. Connect to the database:

   **Windows:**
   ```bash
   psql -U postgres -d ledguyapp
   ```

   **macOS/Linux:**
   ```bash
   sudo -u postgres psql -d ledguyapp
   ```

2. List tables:
   ```sql
   \dt
   ```

   You should see the following tables:
   - makes
   - models
   - types
   - bulb_compatibility

3. Check if data was imported correctly:
   ```sql
   SELECT COUNT(*) FROM makes;
   SELECT COUNT(*) FROM models;
   SELECT COUNT(*) FROM types;
   SELECT COUNT(*) FROM bulb_compatibility;
   ```

4. Exit the PostgreSQL prompt:
   ```sql
   \q
   ```

## Troubleshooting

### Connection Issues

If you encounter connection issues:

1. Check if PostgreSQL is running:
   
   **Windows:**
   ```bash
   sc query postgresql
   ```

   **macOS:**
   ```bash
   brew services list | grep postgresql
   ```

   **Linux:**
   ```bash
   sudo systemctl status postgresql
   ```

2. Verify your connection settings in the `.env` file.

3. Make sure your PostgreSQL is configured to accept connections:
   - Check `pg_hba.conf` file in your PostgreSQL data directory.
   - Ensure it allows connections from localhost.

### Import Data Issues

If you encounter issues importing data:

1. Check the CSV file format:
   - Ensure the column names match what the import script expects.
   - Make sure there are no special characters or encoding issues.

2. Check the console output for specific error messages.

3. Try running the import script with verbose logging:
   ```bash
   NODE_DEBUG=* node db/import-data.js
   ```
