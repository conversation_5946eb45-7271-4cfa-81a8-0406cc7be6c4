{
  "name": "ledguys-backend",
  "version": "1.0.0",
  "description": "Backend for LED Guys Vehicle LED Bulb Lookup Tool",
  "main": "server.js",
  "engines": {
    "node": ">=20.0.0"
  },
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "import-data": "node db/import-data.js",
    "update-schema": "node db/update-schema-images.js",
    "update-image-urls": "node db/update-image-urls.js",
    "update-field-names": "node db/update-schema-field-names.js",
    "fix-image-fk": "node db/fix-image-foreign-key.js",
    "direct-fix-fk": "node db/direct-fix-fk.js",
    "check-db": "node db/check-db-structure.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "dependencies": {
    "cors": "^2.8.5",
    "csv-parse": "^5.6.0",
    "dotenv": "^16.4.5",
    "express": "^4.19.2",
    "multer": "^1.4.5-lts.1",
    "pg": "^8.11.3",
    "pg-copy-streams": "^6.0.6",
    "stream-transform": "^3.3.3"
  },
  "devDependencies": {
<<<<<<< HEAD
    "nodemon": "^3.1.10"
=======
    "nodemon": "^3.1.0"
>>>>>>> 657f6855a2a1da90a1a0758d7b7e012250647dce
  }
}
