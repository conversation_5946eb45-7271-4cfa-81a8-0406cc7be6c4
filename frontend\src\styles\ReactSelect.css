/* React Select Custom Styles */
.react-select-container {
  width: 100%;
  font-size: 16px;
  position: relative;
}

/* Fix for menu positioning */
.react-select__menu-portal {
  z-index: 9999 !important;
}

/* Ensure the dropdown is clickable */
.react-select__dropdown-indicator {
  cursor: pointer;
}

/* Make sure the control has proper height */
.react-select__control {
  min-height: 42px !important;
}

/* Fix for dropdown options */
.react-select__option {
  cursor: pointer !important;
  padding: 8px 12px !important;
}

/* Fix for placeholder text */
.react-select__placeholder {
  color: #6c757d !important;
}

/* Fix for single value text */
.react-select__single-value {
  color: #333 !important;
}

/* Fix for loading indicator */
.react-select__loading-indicator {
  color: #007bff !important;
}

/* Fix for clear indicator */
.react-select__clear-indicator {
  cursor: pointer !important;
}

/* Fix for dropdown indicator */
.react-select__dropdown-indicator {
  padding: 8px !important;
}

/* Fix for indicator separator */
.react-select__indicator-separator {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

/* Fix for value container */
.react-select__value-container {
  padding: 2px 8px !important;
}

/* Fix for input field */
.react-select__input-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix for multi-value */
.react-select__multi-value {
  background-color: rgba(0, 123, 255, 0.1) !important;
}

/* Fix for multi-value label */
.react-select__multi-value__label {
  color: #333 !important;
}

/* Fix for multi-value remove */
.react-select__multi-value__remove {
  color: #666 !important;
  cursor: pointer !important;
}

/* Fix for multi-value remove hover */
.react-select__multi-value__remove:hover {
  background-color: #ff6b6b !important;
  color: white !important;
}
