import React, { useState, useEffect, useRef } from 'react';
import apiService from '../services/api';
import LoadingIndicator from './LoadingIndicator';
import ErrorMessage from './ErrorMessage';
import ReactSelectDropdown from './ReactSelectDropdown';

const SelectionArea = ({ isConnected, onSelectionComplete }) => {
  // Selection state
  const [years, setYears] = useState([]);
  const [makes, setMakes] = useState([]);
  const [models, setModels] = useState([]);
  const [types, setTypes] = useState([]);

  // Selected values
  const [selectedYear, setSelectedYear] = useState('');
  const [selectedMake, setSelectedMake] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [selectedType, setSelectedType] = useState('');

  // Loading states
  const [loadingYears, setLoadingYears] = useState(false);
  const [loadingMakes, setLoadingMakes] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [loadingTypes, setLoadingTypes] = useState(false);

  // Sticky state
  const [isSticky, setIsSticky] = useState(false);
  const selectionAreaRef = useRef(null);

  // Error state
  const [error, setError] = useState(null);

  // Load years when connected
  useEffect(() => {
    if (isConnected) {
      loadYears();
    } else {
      // Reset all selections when disconnected
      resetSelections();
    }
  }, [isConnected]);

  // Add scroll event listener to track sticky state
  useEffect(() => {
    const handleScroll = () => {
      if (selectionAreaRef.current) {
        const { top } = selectionAreaRef.current.getBoundingClientRect();
        // If the top of the element is at or near our sticky position (10px), it's sticky
        setIsSticky(top <= 15);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Load makes when year is selected
  useEffect(() => {
    if (selectedYear) {
      loadMakes(selectedYear);
    } else {
      setMakes([]);
      setSelectedMake('');
    }
  }, [selectedYear]);

  // Load models when make is selected
  useEffect(() => {
    if (selectedYear && selectedMake) {
      loadModels(selectedYear, selectedMake);
    } else {
      setModels([]);
      setSelectedModel('');
    }
  }, [selectedYear, selectedMake]);

  // Load types when model is selected
  useEffect(() => {
    if (selectedYear && selectedMake && selectedModel) {
      loadTypes(selectedYear, selectedMake, selectedModel);
    } else {
      setTypes([]);
      setSelectedType('');
    }
  }, [selectedYear, selectedMake, selectedModel]);

  // Notify parent component when selections are made
  useEffect(() => {
    // Show results when year, make, and model are selected (type is optional)
    if (selectedYear && selectedMake && selectedModel) {
      onSelectionComplete({
        year: selectedYear,
        make: selectedMake,
        model: selectedModel,
        type: selectedType // This can be empty, and results will still show
      });
    } else {
      onSelectionComplete(null);
    }
  }, [selectedYear, selectedMake, selectedModel, selectedType, onSelectionComplete]);

  // Load years from API
  const loadYears = async () => {
    setLoadingYears(true);
    setError(null);

    try {
      const data = await apiService.getYears();
      setYears(data);
    } catch (err) {
      setError('Failed to load years. Please try reconnecting.');
    } finally {
      setLoadingYears(false);
    }
  };

  // Load makes from API
  const loadMakes = async (year) => {
    setLoadingMakes(true);
    setError(null);

    try {
      const data = await apiService.getMakesByYear(year);
      setMakes(data);
    } catch (err) {
      setError('Failed to load makes. Please try again.');
    } finally {
      setLoadingMakes(false);
    }
  };

  // Load models from API
  const loadModels = async (year, make) => {
    setLoadingModels(true);
    setError(null);

    try {
      const data = await apiService.getModelsByYearAndMake(year, make);
      setModels(data);
    } catch (err) {
      setError('Failed to load models. Please try again.');
    } finally {
      setLoadingModels(false);
    }
  };

  // Load types from API
  const loadTypes = async (year, make, model) => {
    setLoadingTypes(true);
    setError(null);

    try {
      const response = await apiService.getTypesByYearMakeModel(year, make, model);

      // Always set the available types
      setTypes(response.types || []);

      // No need to trigger onSelectionComplete here anymore
      // since we now show results as soon as year, make, and model are selected
      // The type selection will just act as a filter

    } catch (err) {
      console.error('Error loading types:', err);
      setError('Failed to load types. Please try again.');
    } finally {
      setLoadingTypes(false);
    }
  };

  // Reset all selections
  const resetSelections = () => {
    setYears([]);
    setMakes([]);
    setModels([]);
    setTypes([]);
    setSelectedYear('');
    setSelectedMake('');
    setSelectedModel('');
    setSelectedType('');
    setError(null);
  };

  // Handle year selection
  const handleYearChange = (year) => {
    setSelectedYear(year);
    setSelectedMake('');
    setSelectedModel('');
    setSelectedType('');
  };

  // Handle make selection
  const handleMakeChange = (makeId) => {
    setSelectedMake(makeId);
    setSelectedModel('');
    setSelectedType('');
  };

  // Handle model selection
  const handleModelChange = (modelId) => {
    setSelectedModel(modelId);
    setSelectedType('');
  };

  // Handle type selection
  const handleTypeChange = (typeId) => {
    setSelectedType(typeId);
  };

  return (
    <div
      ref={selectionAreaRef}
      className={`selection-area ${isSticky ? 'is-sticky' : ''}`}
    >
      <div className="selection-header">
        <h2><i className="fas fa-search"></i> Quick Search</h2>
        {isConnected && (
          <div className="selection-progress">
            <div className={`progress-step ${selectedYear ? 'complete' : ''}`}>
              <i className="fas fa-calendar-alt"></i>
            </div>
            <div className="progress-line"></div>
            <div className={`progress-step ${selectedMake ? 'complete' : ''}`}>
              <i className="fas fa-car"></i>
            </div>
            <div className="progress-line"></div>
            <div className={`progress-step ${selectedModel ? 'complete' : ''}`}>
              <i className="fas fa-car-side"></i>
            </div>
            <div className="progress-line"></div>
            <div className={`progress-step ${selectedType ? 'complete' : ''}`}>
              <i className="fas fa-lightbulb"></i>
            </div>
          </div>
        )}
      </div>

      {error && <ErrorMessage message={error} />}

      <div className="selection-row">
        {/* Year Selection */}
        <div className="selection-group">
          <label className="selection-label">
            <i className="fas fa-calendar-alt"></i> Year
          </label>
          <ReactSelectDropdown
            options={years.map(year => ({ value: year, label: year }))}
            value={selectedYear}
            onChange={handleYearChange}
            placeholder="Select Year"
            isDisabled={!isConnected || loadingYears}
            isLoading={loadingYears}
          />
        </div>

        {/* Make Selection */}
        <div className="selection-group">
          <label className="selection-label">
            <i className="fas fa-car"></i> Make
          </label>
          <ReactSelectDropdown
            options={makes.map(make => ({ value: make.name, label: make.name }))}
            value={selectedMake}
            onChange={handleMakeChange}
            placeholder="Select Make"
            isDisabled={!selectedYear || loadingMakes}
            isLoading={loadingMakes}
          />
        </div>

        {/* Model Selection */}
        <div className="selection-group">
          <label className="selection-label">
            <i className="fas fa-car-side"></i> Model
          </label>
          <ReactSelectDropdown
            options={models.map(model => ({ value: model.name, label: model.name }))}
            value={selectedModel}
            onChange={handleModelChange}
            placeholder="Select Model"
            isDisabled={!selectedMake || loadingModels}
            isLoading={loadingModels}
          />
        </div>

        {/* Type Selection */}
        <div className="selection-group">
          <label className="selection-label">
            <i className="fas fa-lightbulb"></i> Type <span className="optional-label">(Optional - Filters Results)</span>
          </label>
          <ReactSelectDropdown
            options={types.map(type => ({ value: type.name, label: type.name }))}
            value={selectedType}
            onChange={handleTypeChange}
            placeholder="Select Type (Optional)"
            isDisabled={!selectedModel || loadingTypes}
            isLoading={loadingTypes}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectionArea;
