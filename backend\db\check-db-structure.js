/**
 * <PERSON><PERSON><PERSON> to check the current database structure
 * This will help us understand the state of the tables and constraints
 */
const { pool } = require('./connection');

const checkDbStructure = async () => {
  const client = await pool.connect();

  try {
    console.log('Checking database structure...');

    // Check tables
    console.log('\n--- Tables ---');
    const tables = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    tables.rows.forEach(row => {
      console.log(`- ${row.table_name}`);
    });

    // Check if bulb_compatibility exists
    const checkBulbCompat = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'bulb_compatibility'
      )
    `);

    if (checkBulbCompat.rows[0].exists) {
      console.log('\nTable bulb_compatibility exists');

      // Get column info
      const columns = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'bulb_compatibility'
        ORDER BY ordinal_position
      `);

      console.log('Columns:');
      columns.rows.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type})`);
      });

      // Check for constraints
      console.log('\nConstraints on bulb_compatibility:');
      const constraints = await client.query(`
        SELECT con.conname as constraint_name,
               con.contype as constraint_type,
               pg_get_constraintdef(con.oid) as constraint_definition
        FROM pg_constraint con
        JOIN pg_class rel ON rel.oid = con.conrelid
        WHERE rel.relname = 'bulb_compatibility'
      `);

      if (constraints.rows.length === 0) {
        console.log('No constraints found');
      } else {
        constraints.rows.forEach(constraint => {
          let type = '';
          switch(constraint.constraint_type) {
            case 'c': type = 'CHECK'; break;
            case 'f': type = 'FOREIGN KEY'; break;
            case 'p': type = 'PRIMARY KEY'; break;
            case 'u': type = 'UNIQUE'; break;
            default: type = constraint.constraint_type;
          }
          console.log(`- ${constraint.constraint_name} (${type}): ${constraint.constraint_definition}`);
        });
      }
    } else {
      console.log('\nTable bulb_compatibility does NOT exist');
    }

    // Check if bulb_compatibility_old exists
    const checkBulbCompatOld = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'bulb_compatibility_old'
      )
    `);

    if (checkBulbCompatOld.rows[0].exists) {
      console.log('\nTable bulb_compatibility_old exists');

      // Get column info
      const columns = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'bulb_compatibility_old'
        ORDER BY ordinal_position
      `);

      console.log('Columns:');
      columns.rows.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type})`);
      });
    } else {
      console.log('\nTable bulb_compatibility_old does NOT exist');
    }

    // Check if images table exists
    const checkImages = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'images'
      )
    `);

    if (checkImages.rows[0].exists) {
      console.log('\nTable images exists');

      // Get column info
      const columns = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'images'
        ORDER BY ordinal_position
      `);

      console.log('Columns:');
      columns.rows.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type})`);
      });

      // Check foreign key constraints
      console.log('\n--- Foreign Key Constraints on images table ---');
      const fkConstraints = await client.query(`
        SELECT
          tc.constraint_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM
          information_schema.table_constraints AS tc
          JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE
          tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name = 'images'
      `);

      if (fkConstraints.rows.length === 0) {
        console.log('No foreign key constraints found on images table');
      } else {
        fkConstraints.rows.forEach(fk => {
          console.log(`- ${fk.constraint_name}: ${fk.column_name} references ${fk.foreign_table_name}(${fk.foreign_column_name})`);
        });
      }
    } else {
      console.log('\nTable images does NOT exist');
    }

    // Check if there are any records in bulb_compatibility with the ID from the error
    const errorId = 119960; // The ID from the error message
    const checkRecord = await client.query(`
      SELECT EXISTS (
        SELECT FROM bulb_compatibility
        WHERE id = $1
      )
    `, [errorId]);

    console.log(`\nRecord with ID ${errorId} exists in bulb_compatibility: ${checkRecord.rows[0].exists}`);

    // Check if there are any records in bulb_compatibility_old with the ID from the error
    if (checkBulbCompatOld.rows[0].exists) {
      const checkOldRecord = await client.query(`
        SELECT EXISTS (
          SELECT FROM bulb_compatibility_old
          WHERE id = $1
        )
      `, [errorId]);

      console.log(`Record with ID ${errorId} exists in bulb_compatibility_old: ${checkOldRecord.rows[0].exists}`);
    }

  } catch (error) {
    console.error('Error checking database structure:', error);
  } finally {
    client.release();
    await pool.end();
  }
};

// Run the check
checkDbStructure()
  .then(() => {
    console.log('\nDatabase structure check completed.');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Database structure check failed:', err);
    process.exit(1);
  });
