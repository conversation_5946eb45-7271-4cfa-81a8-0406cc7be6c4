# LED Guys Vehicle LED Bulb Lookup Tool - User Guide

This guide provides comprehensive instructions for setting up, configuring, and using the LED Guys Vehicle LED Bulb Lookup Tool.

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Requirements](#system-requirements)
3. [Installation](#installation)
4. [Database Setup](#database-setup)
5. [Running the Application](#running-the-application)
6. [Using the Application](#using-the-application)
7. [Data Management](#data-management)
8. [Troubleshooting](#troubleshooting)
9. [Additional Commands](#additional-commands)

## Project Overview

The LED Guys Vehicle LED Bulb Lookup Tool is a web application that allows users to:

- Search for LED bulb compatibility by year, make, model, and bulb type
- View detailed compatibility information including display codes and package details
- Upload and view images for compatible products
- Add LED Guys specific data and descriptions
- Import compatibility data via CSV files

## System Requirements

### Backend
- Node.js (v20 or higher)
- PostgreSQL (v12 or higher)

### Frontend
- Modern web browser (Chrome, Firefox, Edge, Safari)

## Installation

### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd Ledguys
```

### Step 2: Install Backend Dependencies
```bash
cd backend
npm install
```

### Step 3: Install Frontend Dependencies
```bash
cd ../frontend
npm install
```

## Database Setup

### Step 1: Create PostgreSQL Database
1. Install PostgreSQL if not already installed
2. Create a new database:
```sql
CREATE DATABASE ledguys;
```

### Step 2: Configure Database Connection
1. Create a `.env` file in the `backend` directory with the following content:
```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ledguys
DB_USER=postgres
DB_PASSWORD=root
PORT=5000
```
2. Adjust the values according to your PostgreSQL configuration

### Step 3: Initialize Database Schema
1. Navigate to the backend directory:
```bash
cd backend
```
2. Run the schema update script:
```bash
npm run update-field-names
```
This will create the necessary tables with the correct field names.

## Database Schema

The main table in the database is `bulb_compatibility` with the following structure:

| Field Name     | Data Type    | Description                       |
|----------------|--------------|-----------------------------------|
| id             | SERIAL       | Primary key                       |
| car_year       | INTEGER      | Vehicle year                      |
| car_make       | VARCHAR(50)  | Vehicle manufacturer              |
| car_model      | VARCHAR(50)  | Vehicle model                     |
| blub_type      | VARCHAR(50)  | Type of LED bulb                  |
| dis_type       | VARCHAR(50)  | Display type                      |
| dis_code       | VARCHAR(50)  | Display code                      |
| pack_id        | VARCHAR(50)  | Package ID                        |
| pack_name      | VARCHAR(250) | Package name                      |
| productvalue   | VARCHAR(50)  | Product value/code                |
| ledguys_data   | VARCHAR(500) | LED Guys specific data            |
| ledguys_desc   | VARCHAR(500) | LED Guys description              |
| image_url      | VARCHAR(500) | URL to product image              |

## Running the Application

### Start the Backend Server
```bash
cd backend
npm run dev
```
The backend server will start on http://localhost:5000

### Start the Frontend Development Server
```bash
cd frontend
npm start
```
The frontend will be available at http://localhost:3000

## Using the Application

### Bulb Lookup
1. Select a year, make, model, and optionally a bulb type
2. View compatibility results in the grid
3. Click the "+" button to view LED Guys data and description
4. Click "Add Image" to upload an image and add LED Guys data

### CSV Data Upload
1. Click the "CSV Upload" button in the navigation
2. Select the file type (compatibility)
3. Review the required format information
4. Upload your CSV file
5. Click "Import Data" to process the uploaded file

## Data Management

### CSV File Format
The compatibility CSV file should have the following columns:

- `car_year` (INTEGER): Vehicle year
- `car_make` (VARCHAR): Vehicle manufacturer
- `car_model` (VARCHAR): Vehicle model
- `blub_type` (VARCHAR): Type of LED bulb
- `dis_type` (VARCHAR): Display type
- `dis_code` (VARCHAR): Display code
- `pack_id` (VARCHAR): Package ID
- `pack_name` (VARCHAR): Package name
- `productvalue` (VARCHAR): Product value/code

Example CSV row:
```csv
car_year,car_make,car_model,blub_type,dis_type,dis_code,pack_id,pack_name,productvalue
2023,Acura,RDX,Stepwell,LED,A-RDX-SW-23,PKG001,Stepwell LED,SW-LED-001
```

### Handling Large CSV Files
The application supports uploading large CSV files (5-10 GB). The upload process:

1. Uploads the file to the server
2. Processes the data in batches
3. Provides progress feedback

## Troubleshooting

### Database Connection Issues
- Verify PostgreSQL is running
- Check the `.env` file for correct database credentials
- Ensure the database exists and is accessible

### CSV Import Issues
- Verify the CSV file has the correct column headers
- Check for special characters or formatting issues in the CSV
- For very large files, increase the Node.js memory limit:
  ```bash
  NODE_OPTIONS=--max-old-space-size=4096 npm run dev
  ```

### Image Upload Issues
- Verify the `uploads` directory exists in the backend folder and is writable
- Check that the image file is a valid format (JPG, PNG, GIF)
- Ensure the image size is under 5MB
- If you get a foreign key constraint error, run the direct-fix-fk script:
  ```bash
  cd backend
  npm run direct-fix-fk
  ```

  Or run the batch file:
  ```
  backend\direct-fix-fk.bat
  ```

- To check the database structure and diagnose issues:
  ```bash
  cd backend
  npm run check-db
  ```

  Or run the batch file:
  ```
  backend\check-db.bat
  ```

## Additional Commands

### Import Data from CSV
```bash
cd backend
npm run import-data
```

### Update Database Schema
```bash
cd backend
npm run update-field-names
```

### Update Image URLs
```bash
cd backend
npm run update-image-urls
```

### Fix Image Foreign Key Constraint
```bash
cd backend
npm run direct-fix-fk
```

### Check Database Structure
```bash
cd backend
npm run check-db
```

---

For additional support or questions, please contact the development team.