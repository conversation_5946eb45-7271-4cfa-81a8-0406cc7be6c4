import React, { useState, useCallback } from 'react';
import Header from './components/Header';
import ConnectionManager from './components/ConnectionManager';
import SelectionArea from './components/SelectionArea';
import ResultsGrid from './components/ResultsGrid';
import CSVUpload from './components/CSVUpload';
import './styles/App.css';

function App() {
  const [isConnected, setIsConnected] = useState(false);
  const [selection, setSelection] = useState(null);
  const [activeView, setActiveView] = useState('lookup'); // 'lookup' or 'upload'

  const handleConnectionChange = (connected) => {
    setIsConnected(connected);
    if (!connected) {
      setSelection(null);
    }
  };

  const handleSelectionComplete = useCallback((selectionData) => {
    setSelection(selectionData);
  }, []);

  return (
    <div className="app">
      <Header />

      <div className="container">
        <ConnectionManager onConnectionChange={handleConnectionChange} />

        <div className="view-toggle">
          <button
            className={`btn ${activeView === 'lookup' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setActiveView('lookup')}
          >
            Quick Search
          </button>
          <button
            className={`btn ${activeView === 'upload' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setActiveView('upload')}
          >
            CSV Upload
          </button>
        </div>

        {activeView === 'lookup' ? (
          <>
            <SelectionArea
              isConnected={isConnected}
              onSelectionComplete={handleSelectionComplete}
            />

            <ResultsGrid selection={selection} />
          </>
        ) : (
          <CSVUpload />
        )}
      </div>

      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-logo">
              <img src="/images/image.png" alt="LED Guys Logo" className="footer-logo-img" />
            </div>
            <div className="footer-info">
              <p>&copy; {new Date().getFullYear()} LED Guys. All rights reserved.</p>
              <p className="footer-tagline">Professional LED Lighting Solutions</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
